/**
 * Cloudinary Setup - 基础Cloudinary配置和初始化
 * 为向后兼容性提供基础的Cloudinary功能
 */

// 基础Cloudinary视频管理器
class CloudinaryVideoManager {
    constructor(cloudName) {
        this.cloudName = cloudName;
        this.baseUrl = `https://res.cloudinary.com/${cloudName}`;
        console.log(`☁️ Cloudinary Video Manager initialized for: ${cloudName}`);
    }

    // 获取视频URL
    getVideoUrl(publicId, transformations = '') {
        const baseTransform = 'q_auto,f_auto';
        const finalTransform = transformations ? `${baseTransform},${transformations}` : baseTransform;
        return `${this.baseUrl}/video/upload/${finalTransform}/${publicId}.mp4`;
    }

    // 创建视频元素
    async createVideoElement(publicId, transformations = '') {
        return new Promise((resolve, reject) => {
            const video = document.createElement('video');
            video.autoplay = true;
            video.muted = true;
            video.loop = true;
            video.playsInline = true;
            video.preload = 'auto';

            const source = document.createElement('source');
            source.src = this.getVideoUrl(publicId, transformations);
            source.type = 'video/mp4';
            
            video.appendChild(source);

            video.addEventListener('loadeddata', () => {
                console.log(`✅ Cloudinary video loaded: ${publicId}`);
                resolve(video);
            });

            video.addEventListener('error', (error) => {
                console.error(`❌ Cloudinary video load failed: ${publicId}`, error);
                reject(error);
            });

            // 超时处理
            setTimeout(() => {
                reject(new Error(`Cloudinary video load timeout: ${publicId}`));
            }, 15000);
        });
    }

    // 检查视频是否可用
    async checkVideoAvailability(publicId) {
        try {
            const url = this.getVideoUrl(publicId);
            const response = await fetch(url, { method: 'HEAD' });
            return response.ok;
        } catch (error) {
            console.error(`Error checking video availability: ${publicId}`, error);
            return false;
        }
    }
}

// 全局初始化
if (typeof window !== 'undefined') {
    // 确保CONFIG已加载
    if (window.CONFIG && window.CONFIG.CLOUDINARY && window.CONFIG.CLOUDINARY.ENABLED) {
        // 创建全局Cloudinary管理器实例
        window.CloudinaryVideoManager = CloudinaryVideoManager;
        
        // 创建默认实例
        if (window.CONFIG.CLOUDINARY.CLOUD_NAME) {
            window.cloudinaryManager = new CloudinaryVideoManager(window.CONFIG.CLOUDINARY.CLOUD_NAME);
        }
        
        console.log('☁️ Cloudinary setup completed');
    } else {
        console.log('⚠️ Cloudinary disabled or CONFIG not loaded');
        
        // 提供空的管理器以避免错误
        window.CloudinaryVideoManager = class {
            constructor() {}
            async createVideoElement() { return null; }
            async checkVideoAvailability() { return false; }
        };
    }
}

// 导出（如果支持模块）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CloudinaryVideoManager;
}
