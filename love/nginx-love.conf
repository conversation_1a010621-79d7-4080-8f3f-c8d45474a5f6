# Love Website Nginx Configuration
# 爱情网站的Nginx反向代理配置

server {
    listen 80;
    server_name love.yuh.cool;

    # 临时使用HTTP，稍后配置HTTPS
    # return 301 https://$server_name$request_uri;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # 日志配置
    access_log /var/log/nginx/love.yuh.cool.access.log;
    error_log /var/log/nginx/love.yuh.cool.error.log;
    
    # 客户端上传限制
    client_max_body_size 100M;
    
    # 超时配置
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    # 反向代理到Node.js应用
    location / {
        proxy_pass http://127.0.0.1:1314;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 处理WebSocket连接
        proxy_set_header Connection "upgrade";
        proxy_set_header Upgrade $http_upgrade;
    }
    
    # 静态资源缓存优化
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://127.0.0.1:1314;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存设置
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }
    
    # 视频文件特殊处理
    location ~* \.(mp4|webm|ogg|avi|mov)$ {
        proxy_pass http://127.0.0.1:1314;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 支持范围请求（重要：视频播放需要）
        proxy_set_header Range $http_range;
        proxy_set_header If-Range $http_if_range;
        proxy_no_cache $http_range $http_if_range;
        
        # 视频文件缓存
        expires 30d;
        add_header Cache-Control "public";
    }
    
    # API接口
    location /api/ {
        proxy_pass http://127.0.0.1:1314;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API不缓存
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:1314;
        proxy_set_header Host $host;
        access_log off;
    }
    
    # 错误页面
    error_page 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
