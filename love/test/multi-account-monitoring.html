<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多账户Cloudinary监控面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .account-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #4CAF50;
        }

        .account-card.error {
            border-left-color: #f44336;
        }

        .account-card.warning {
            border-left-color: #ff9800;
        }

        .account-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .account-name {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }

        .account-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-active {
            background: #4CAF50;
            color: white;
        }

        .status-error {
            background: #f44336;
            color: white;
        }

        .status-standby {
            background: #ff9800;
            color: white;
        }

        .quota-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .quota-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            transition: width 0.3s ease;
        }

        .quota-fill.warning {
            background: linear-gradient(90deg, #ff9800, #ffb74d);
        }

        .quota-fill.danger {
            background: linear-gradient(90deg, #f44336, #ef5350);
        }

        .account-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 0.9em;
            color: #666;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
        }

        .summary-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .summary-item {
            text-align: center;
        }

        .summary-value {
            font-size: 2em;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 5px;
        }

        .summary-label {
            color: #666;
            font-size: 0.9em;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #2196F3;
            color: white;
        }

        .btn-primary:hover {
            background: #1976D2;
        }

        .btn-success {
            background: #4CAF50;
            color: white;
        }

        .btn-success:hover {
            background: #388E3C;
        }

        .btn-warning {
            background: #ff9800;
            color: white;
        }

        .btn-warning:hover {
            background: #F57C00;
        }

        .logs-section {
            background: #1e1e1e;
            color: #00ff00;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
        }

        .timestamp {
            color: #888;
        }

        .error-log {
            color: #ff6b6b;
        }

        .success-log {
            color: #51cf66;
        }

        .warning-log {
            color: #ffd43b;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 多账户Cloudinary监控面板</h1>
            <p>实时监控所有Cloudinary账户状态和配额使用情况</p>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="refreshStatus()">🔄 刷新状态</button>
            <button class="btn btn-success" onclick="testAllConnections()">🔍 测试连接</button>
            <button class="btn btn-warning" onclick="clearLogs()">🗑️ 清除日志</button>
        </div>

        <div class="summary-section">
            <h3>📊 总体状态</h3>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-value" id="totalAccounts">6</div>
                    <div class="summary-label">总账户数</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" id="activeAccounts">5</div>
                    <div class="summary-label">活跃账户</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" id="totalQuota">150GB</div>
                    <div class="summary-label">总配额</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" id="usedQuota">12.5GB</div>
                    <div class="summary-label">已使用</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" id="usagePercent">8.3%</div>
                    <div class="summary-label">使用率</div>
                </div>
            </div>
        </div>

        <div class="status-grid" id="accountsGrid">
            <!-- 账户卡片将通过JavaScript动态生成 -->
        </div>

        <div class="summary-section">
            <h3>📝 系统日志</h3>
            <div class="logs-section" id="systemLogs">
                <div class="timestamp">[2025-01-30 10:00:00]</div>
                <div class="success-log">✅ 多账户监控系统启动</div>
                <div class="timestamp">[2025-01-30 10:00:01]</div>
                <div class="success-log">🔧 正在初始化账户配置...</div>
            </div>
        </div>
    </div>

    <!-- 引入配置文件和管理器 -->
    <script src="/config.js"></script>
    <script src="/hybrid-cdn-manager.js"></script>
    <script src="/cloudinary-load-balancer.js"></script>

    <script>
        // 全局变量
        let hybridManager = null;
        let loadBalancer = null;
        let refreshInterval = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeManagers();
            refreshStatus();
            startAutoRefresh();
            addLog('🚀 多账户监控面板已启动', 'success');
        });

        // 初始化管理器
        function initializeManagers() {
            try {
                hybridManager = new HybridCDNManager();
                loadBalancer = new CloudinaryLoadBalancer();
                addLog('✅ CDN管理器初始化成功', 'success');
            } catch (error) {
                addLog(`❌ 管理器初始化失败: ${error.message}`, 'error');
            }
        }

        // 刷新状态
        function refreshStatus() {
            if (!hybridManager) {
                addLog('⚠️ CDN管理器未初始化', 'warning');
                return;
            }

            try {
                const status = hybridManager.getSystemStatus();
                updateSummary(status.summary);
                updateAccountsGrid(status.sources);
                addLog('🔄 状态刷新完成', 'success');
            } catch (error) {
                addLog(`❌ 状态刷新失败: ${error.message}`, 'error');
            }
        }

        // 更新总体状态
        function updateSummary(summary) {
            document.getElementById('totalAccounts').textContent = summary.totalAccounts || 6;
            document.getElementById('activeAccounts').textContent = summary.activeAccounts || 0;
            document.getElementById('totalQuota').textContent = `${summary.totalQuota || 150}GB`;
            document.getElementById('usedQuota').textContent = `${summary.usedQuota?.toFixed(1) || 0}GB`;
            document.getElementById('usagePercent').textContent = `${summary.overallUsagePercent || 0}%`;
        }

        // 更新账户网格
        function updateAccountsGrid(sources) {
            const grid = document.getElementById('accountsGrid');
            grid.innerHTML = '';

            const cloudinarySources = sources.filter(s => s.type === 'cloudinary');
            
            cloudinarySources.forEach(source => {
                const card = createAccountCard(source);
                grid.appendChild(card);
            });
        }

        // 创建账户卡片
        function createAccountCard(source) {
            const card = document.createElement('div');
            card.className = `account-card ${getCardClass(source)}`;
            
            const usagePercent = parseFloat(source.usagePercent) || 0;
            const quotaClass = usagePercent > 90 ? 'danger' : usagePercent > 75 ? 'warning' : '';
            
            card.innerHTML = `
                <div class="account-header">
                    <div class="account-name">${source.cloudName}</div>
                    <div class="account-status status-${source.status}">${source.status}</div>
                </div>
                <div class="account-info">
                    <div class="info-item">
                        <span>分配页面:</span>
                        <span>${source.assignedPages?.join(', ') || '无'}</span>
                    </div>
                    <div class="info-item">
                        <span>优先级:</span>
                        <span>${source.priority}</span>
                    </div>
                </div>
                <div class="quota-bar">
                    <div class="quota-fill ${quotaClass}" style="width: ${usagePercent}%"></div>
                </div>
                <div class="account-info">
                    <div class="info-item">
                        <span>配额使用:</span>
                        <span>${source.used?.toFixed(1) || 0}GB / ${source.quota}GB</span>
                    </div>
                    <div class="info-item">
                        <span>使用率:</span>
                        <span>${usagePercent.toFixed(1)}%</span>
                    </div>
                    ${source.health ? `
                    <div class="info-item">
                        <span>成功率:</span>
                        <span>${source.health.successRate}%</span>
                    </div>
                    <div class="info-item">
                        <span>平均加载:</span>
                        <span>${source.health.avgLoadTime}ms</span>
                    </div>
                    ` : ''}
                </div>
            `;
            
            return card;
        }

        // 获取卡片样式类
        function getCardClass(source) {
            if (source.status === 'error') return 'error';
            if (parseFloat(source.usagePercent) > 90) return 'warning';
            return '';
        }

        // 测试所有连接
        async function testAllConnections() {
            addLog('🔍 开始测试所有账户连接...', 'info');
            
            // 这里可以添加实际的连接测试逻辑
            // 由于前端无法直接访问Cloudinary API，这里只是模拟
            
            setTimeout(() => {
                addLog('✅ 所有账户连接测试完成', 'success');
                refreshStatus();
            }, 2000);
        }

        // 添加日志
        function addLog(message, type = 'info') {
            const logs = document.getElementById('systemLogs');
            const timestamp = new Date().toLocaleString();
            const logClass = type === 'error' ? 'error-log' : 
                            type === 'success' ? 'success-log' : 
                            type === 'warning' ? 'warning-log' : '';
            
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `
                <div class="timestamp">[${timestamp}]</div>
                <div class="${logClass}">${message}</div>
            `;
            
            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
            
            // 限制日志条数
            const logEntries = logs.children;
            if (logEntries.length > 100) {
                logs.removeChild(logEntries[0]);
                logs.removeChild(logEntries[0]); // 移除时间戳和消息两行
            }
        }

        // 清除日志
        function clearLogs() {
            document.getElementById('systemLogs').innerHTML = '';
            addLog('🗑️ 日志已清除', 'info');
        }

        // 开始自动刷新
        function startAutoRefresh() {
            refreshInterval = setInterval(refreshStatus, 30000); // 30秒刷新一次
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            stopAutoRefresh();
        });
    </script>
</body>
</html>
