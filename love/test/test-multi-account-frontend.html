<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多账户前端加载测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .test-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #4CAF50;
        }

        .test-card.loading {
            border-left-color: #ff9800;
        }

        .test-card.error {
            border-left-color: #f44336;
        }

        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .test-name {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }

        .test-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-success {
            background: #4CAF50;
            color: white;
        }

        .status-loading {
            background: #ff9800;
            color: white;
        }

        .status-error {
            background: #f44336;
            color: white;
        }

        .video-container {
            width: 100%;
            height: 200px;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            margin: 10px 0;
            position: relative;
        }

        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-info {
            font-size: 0.9em;
            color: #666;
            margin-top: 10px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #2196F3;
            color: white;
        }

        .btn-primary:hover {
            background: #1976D2;
        }

        .logs-section {
            background: #1e1e1e;
            color: #00ff00;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .timestamp {
            color: #888;
        }

        .error-log {
            color: #ff6b6b;
        }

        .success-log {
            color: #51cf66;
        }

        .warning-log {
            color: #ffd43b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 多账户前端加载测试</h1>
            <p>测试每个页面是否使用对应的Cloudinary账户加载视频</p>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="testAllPages()">🚀 测试所有页面</button>
            <button class="btn btn-primary" onclick="clearLogs()">🗑️ 清除日志</button>
        </div>

        <div class="test-grid" id="testGrid">
            <!-- 测试卡片将通过JavaScript动态生成 -->
        </div>

        <div class="logs-section" id="systemLogs">
            <div class="timestamp">[2025-01-30 12:00:00]</div>
            <div class="success-log">✅ 多账户前端测试系统启动</div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="/config.js"></script>
    <script src="/hybrid-cdn-manager.js"></script>
    <script src="/cloudinary-load-balancer.js"></script>
    <script src="/simple-video-manager.js"></script>

    <script>
        // 页面配置
        const PAGE_CONFIGS = {
            'INDEX': {
                name: '首页',
                expectedAccount: 'dcglebc2w',
                videoId: 'index'
            },
            'ANNIVERSARY': {
                name: '纪念日',
                expectedAccount: 'drhqbbqxz',
                videoId: 'anniversary'
            },
            'MEETINGS': {
                name: '相遇回忆',
                expectedAccount: 'dkqnm9nwr',
                videoId: 'meetings'
            },
            'MEMORIAL': {
                name: '纪念相册',
                expectedAccount: 'ds14sv2gh',
                videoId: 'memorial'
            },
            'TOGETHER_DAYS': {
                name: '在一起的日子',
                expectedAccount: 'dpq95x5nf',
                videoId: 'together_days'
            }
        };

        let hybridManager = null;
        let testResults = {};

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeManagers();
            createTestCards();
            addLog('🚀 多账户前端测试系统已启动', 'success');
        });

        // 初始化管理器
        function initializeManagers() {
            try {
                if (window.HybridCDNManager) {
                    hybridManager = new HybridCDNManager();
                    addLog('✅ 混合CDN管理器初始化成功', 'success');
                } else {
                    addLog('❌ 混合CDN管理器未找到', 'error');
                }
            } catch (error) {
                addLog(`❌ 管理器初始化失败: ${error.message}`, 'error');
            }
        }

        // 创建测试卡片
        function createTestCards() {
            const grid = document.getElementById('testGrid');
            grid.innerHTML = '';

            Object.entries(PAGE_CONFIGS).forEach(([pageKey, config]) => {
                const card = document.createElement('div');
                card.className = 'test-card';
                card.id = `card-${pageKey}`;
                
                card.innerHTML = `
                    <div class="test-header">
                        <div class="test-name">${config.name}</div>
                        <div class="test-status status-loading" id="status-${pageKey}">待测试</div>
                    </div>
                    <div class="video-container" id="video-container-${pageKey}">
                        <video id="video-${pageKey}" muted loop></video>
                    </div>
                    <div class="video-info">
                        <div class="info-item">
                            <span>预期账户:</span>
                            <span>${config.expectedAccount}</span>
                        </div>
                        <div class="info-item">
                            <span>视频ID:</span>
                            <span>${config.videoId}</span>
                        </div>
                        <div class="info-item">
                            <span>加载时间:</span>
                            <span id="loadtime-${pageKey}">-</span>
                        </div>
                        <div class="info-item">
                            <span>实际URL:</span>
                            <span id="actualurl-${pageKey}" style="font-size: 0.8em; word-break: break-all;">-</span>
                        </div>
                    </div>
                `;
                
                grid.appendChild(card);
            });
        }

        // 测试单个页面
        async function testSinglePage(pageKey) {
            const config = PAGE_CONFIGS[pageKey];
            const video = document.getElementById(`video-${pageKey}`);
            const card = document.getElementById(`card-${pageKey}`);
            const status = document.getElementById(`status-${pageKey}`);
            const loadTimeSpan = document.getElementById(`loadtime-${pageKey}`);
            const actualUrlSpan = document.getElementById(`actualurl-${pageKey}`);

            addLog(`🔍 开始测试 ${config.name} (${pageKey})`, 'info');
            
            // 更新状态为加载中
            card.className = 'test-card loading';
            status.textContent = '加载中';
            status.className = 'test-status status-loading';

            try {
                const startTime = performance.now();
                
                // 使用混合CDN管理器加载视频
                const result = await hybridManager.loadVideoBackground(pageKey, video);
                
                const loadTime = performance.now() - startTime;
                loadTimeSpan.textContent = `${loadTime.toFixed(0)}ms`;
                actualUrlSpan.textContent = video.src || '未获取到URL';

                if (result.success) {
                    // 检查是否使用了正确的账户
                    const videoUrl = video.src;
                    const expectedAccount = config.expectedAccount;
                    const isCorrectAccount = videoUrl.includes(expectedAccount);

                    if (isCorrectAccount) {
                        card.className = 'test-card';
                        status.textContent = '成功';
                        status.className = 'test-status status-success';
                        addLog(`✅ ${config.name} 测试成功 - 使用账户: ${expectedAccount}`, 'success');
                        
                        testResults[pageKey] = {
                            success: true,
                            loadTime: loadTime,
                            account: expectedAccount,
                            url: videoUrl
                        };
                    } else {
                        card.className = 'test-card error';
                        status.textContent = '账户错误';
                        status.className = 'test-status status-error';
                        addLog(`⚠️ ${config.name} 使用了错误的账户`, 'warning');
                        
                        testResults[pageKey] = {
                            success: false,
                            error: '使用了错误的账户',
                            expectedAccount: expectedAccount,
                            actualUrl: videoUrl
                        };
                    }
                } else {
                    throw new Error(result.error || '加载失败');
                }

            } catch (error) {
                card.className = 'test-card error';
                status.textContent = '失败';
                status.className = 'test-status status-error';
                addLog(`❌ ${config.name} 测试失败: ${error.message}`, 'error');
                
                testResults[pageKey] = {
                    success: false,
                    error: error.message
                };
            }
        }

        // 测试所有页面
        async function testAllPages() {
            if (!hybridManager) {
                addLog('❌ CDN管理器未初始化', 'error');
                return;
            }

            addLog('🚀 开始测试所有页面...', 'info');
            testResults = {};

            for (const pageKey of Object.keys(PAGE_CONFIGS)) {
                await testSinglePage(pageKey);
                // 等待1秒避免请求过于频繁
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // 生成测试汇总
            generateTestSummary();
        }

        // 生成测试汇总
        function generateTestSummary() {
            const totalTests = Object.keys(testResults).length;
            const successTests = Object.values(testResults).filter(r => r.success).length;
            const successRate = ((successTests / totalTests) * 100).toFixed(1);

            addLog('📊 测试汇总:', 'info');
            addLog(`总测试数: ${totalTests}`, 'info');
            addLog(`成功数: ${successTests}`, 'info');
            addLog(`成功率: ${successRate}%`, 'info');

            if (successTests === totalTests) {
                addLog('🎉 所有测试通过！多账户前端配置工作正常！', 'success');
            } else {
                addLog('⚠️ 部分测试失败，请检查配置', 'warning');
            }
        }

        // 添加日志
        function addLog(message, type = 'info') {
            const logs = document.getElementById('systemLogs');
            const timestamp = new Date().toLocaleString();
            const logClass = type === 'error' ? 'error-log' : 
                            type === 'success' ? 'success-log' : 
                            type === 'warning' ? 'warning-log' : '';
            
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `
                <div class="timestamp">[${timestamp}]</div>
                <div class="${logClass}">${message}</div>
            `;
            
            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
        }

        // 清除日志
        function clearLogs() {
            document.getElementById('systemLogs').innerHTML = '';
            addLog('🗑️ 日志已清除', 'info');
        }
    </script>
</body>
</html>
