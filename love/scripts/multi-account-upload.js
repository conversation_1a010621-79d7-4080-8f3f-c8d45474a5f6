#!/usr/bin/env node

/**
 * 多账户Cloudinary视频上传脚本
 * 根据页面分配将视频上传到对应的Cloudinary账户
 */

const cloudinary = require('cloudinary').v2;
const fs = require('fs');
const path = require('path');

// 多账户配置 - 从环境变量读取API密钥
const ACCOUNTS = {
    'dcglebc2w': {
        cloudName: 'dcglebc2w',
        apiKey: process.env.CLOUDINARY_API_KEY_DCGLEBC2W || '***************',
        apiSecret: process.env.CLOUDINARY_API_SECRET_DCGLEBC2W || 'FfwmlQJX_0LOszwF6YF9KbnhmoU',
        assignedPages: ['INDEX']
    },
    'drhqbbqxz': {
        cloudName: 'drhqbbqxz',
        apiKey: process.env.CLOUDINARY_API_KEY_DRHQBBQXZ || '***************',
        apiSecret: process.env.CLOUDINARY_API_SECRET_DRHQBBQXZ || '7g-JSBacW-ccz1cSAdkHw_wCrU8',
        assignedPages: ['ANNIVERSARY']
    },
    'dkqnm9nwr': {
        cloudName: 'dkqnm9nwr',
        apiKey: process.env.CLOUDINARY_API_KEY_DKQNM9NWR || '***************',
        apiSecret: process.env.CLOUDINARY_API_SECRET_DKQNM9NWR || 'juh_-_Amw-ds0gY03QL-E88oOIQ',
        assignedPages: ['MEETINGS']
    },
    'ds14sv2gh': {
        cloudName: 'ds14sv2gh',
        apiKey: process.env.CLOUDINARY_API_KEY_DS14SV2GH || '***************',
        apiSecret: process.env.CLOUDINARY_API_SECRET_DS14SV2GH || 'ajE1x9E4Ynrg5AioDxJC_EZuTow',
        assignedPages: ['MEMORIAL']
    },
    'dpq95x5nf': {
        cloudName: 'dpq95x5nf',
        apiKey: process.env.CLOUDINARY_API_KEY_DPQ95X5NF || '934251748658618',
        apiSecret: process.env.CLOUDINARY_API_SECRET_DPQ95X5NF || '849z0GBq5fapCwUCaZ0Ct0H4-5Y',
        assignedPages: ['TOGETHER_DAYS']
    },
    'dtsgvqrna': {
        cloudName: 'dtsgvqrna',
        apiKey: process.env.CLOUDINARY_API_KEY_DTSGVQRNA || '567337797774118',
        apiSecret: process.env.CLOUDINARY_API_SECRET_DTSGVQRNA || 'wgHrEwcNyzFyOceB9Q9yAHbteqc',
        assignedPages: [] // 备用账户
    }
};

// 页面到视频文件的映射 - 使用压缩后的高质量文件（保持2K画质）
const PAGE_VIDEO_MAPPING = {
    'INDEX': 'cloudinary-ready/home.mp4',
    'ANNIVERSARY': 'cloudinary-ready/anniversary.mp4',
    'MEETINGS': 'cloudinary-ready/meetings.mp4',
    'MEMORIAL': 'cloudinary-ready/memorial.mp4',
    'TOGETHER_DAYS': 'cloudinary-ready/together-days.mp4'
};

// 页面到账户的映射
const PAGE_ACCOUNT_MAPPING = {
    'INDEX': 'dcglebc2w',
    'ANNIVERSARY': 'drhqbbqxz',
    'MEETINGS': 'dkqnm9nwr', 
    'MEMORIAL': 'ds14sv2gh',
    'TOGETHER_DAYS': 'dpq95x5nf'
};

/**
 * 配置Cloudinary账户
 */
function configureCloudinary(accountId) {
    const account = ACCOUNTS[accountId];
    if (!account) {
        throw new Error(`未找到账户配置: ${accountId}`);
    }

    cloudinary.config({
        cloud_name: account.cloudName,
        api_key: account.apiKey,
        api_secret: account.apiSecret
    });

    console.log(`🔧 配置Cloudinary账户: ${account.cloudName}`);
}

/**
 * 上传视频到指定账户
 */
async function uploadVideoToAccount(accountId, videoPath, publicId) {
    try {
        configureCloudinary(accountId);

        const fullPath = path.join(__dirname, '..', 'background', videoPath);
        
        if (!fs.existsSync(fullPath)) {
            throw new Error(`视频文件不存在: ${fullPath}`);
        }

        console.log(`📤 开始上传: ${videoPath} 到账户 ${accountId}`);
        console.log(`📁 文件路径: ${fullPath}`);
        console.log(`🏷️  Public ID: ${publicId}`);

        const result = await cloudinary.uploader.upload(fullPath, {
            resource_type: 'video',
            public_id: publicId,
            folder: 'love-website',
            overwrite: true,
            chunk_size: 6000000  // 6MB chunks for large files
        });

        console.log(`✅ 上传成功: ${result.secure_url}`);
        console.log(`📊 文件大小: ${(result.bytes / 1024 / 1024).toFixed(2)} MB`);
        
        return result;

    } catch (error) {
        console.error(`❌ 上传失败 (${accountId}):`, error.message);
        throw error;
    }
}

/**
 * 批量上传所有页面的视频
 */
async function uploadAllVideos() {
    console.log('🚀 开始多账户视频上传...\n');

    const results = [];
    
    for (const [pageName, accountId] of Object.entries(PAGE_ACCOUNT_MAPPING)) {
        const videoPath = PAGE_VIDEO_MAPPING[pageName];
        const publicId = pageName.toLowerCase();

        try {
            console.log(`\n📋 处理页面: ${pageName}`);
            console.log(`🎯 目标账户: ${accountId}`);
            console.log(`🎬 视频文件: ${videoPath}`);

            const result = await uploadVideoToAccount(accountId, videoPath, publicId);
            
            results.push({
                page: pageName,
                account: accountId,
                success: true,
                url: result.secure_url,
                size: result.bytes
            });

            // 等待2秒避免API限制
            await new Promise(resolve => setTimeout(resolve, 2000));

        } catch (error) {
            results.push({
                page: pageName,
                account: accountId,
                success: false,
                error: error.message
            });
        }
    }

    // 输出结果汇总
    console.log('\n📊 上传结果汇总:');
    console.log('='.repeat(60));
    
    let successCount = 0;
    let totalSize = 0;

    results.forEach(result => {
        if (result.success) {
            console.log(`✅ ${result.page}: ${result.url}`);
            console.log(`   账户: ${result.account}, 大小: ${(result.size / 1024 / 1024).toFixed(2)} MB`);
            successCount++;
            totalSize += result.size;
        } else {
            console.log(`❌ ${result.page}: ${result.error}`);
            console.log(`   账户: ${result.account}`);
        }
    });

    console.log('\n📈 统计信息:');
    console.log(`成功上传: ${successCount}/${results.length}`);
    console.log(`总大小: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
    
    if (successCount === results.length) {
        console.log('\n🎉 所有视频上传完成！');
    } else {
        console.log('\n⚠️  部分视频上传失败，请检查错误信息');
    }

    return results;
}

/**
 * 测试账户连接
 */
async function testAccountConnection(accountId) {
    try {
        configureCloudinary(accountId);
        
        const result = await cloudinary.api.ping();
        console.log(`✅ 账户 ${accountId} 连接正常`);
        return true;
    } catch (error) {
        console.error(`❌ 账户 ${accountId} 连接失败:`, error.message);
        return false;
    }
}

/**
 * 测试所有账户连接
 */
async function testAllConnections() {
    console.log('🔍 测试所有账户连接...\n');
    
    const results = [];
    
    for (const accountId of Object.keys(ACCOUNTS)) {
        const success = await testAccountConnection(accountId);
        results.push({ accountId, success });
        
        // 等待1秒
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    const successCount = results.filter(r => r.success).length;
    console.log(`\n📊 连接测试结果: ${successCount}/${results.length} 成功`);
    
    return results;
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    const command = args[0];

    try {
        switch (command) {
            case 'test':
                await testAllConnections();
                break;
            case 'upload':
                await uploadAllVideos();
                break;
            case 'upload-page':
                const pageName = args[1];
                if (!pageName || !PAGE_ACCOUNT_MAPPING[pageName]) {
                    console.error('❌ 请指定有效的页面名称: INDEX, ANNIVERSARY, MEETINGS, MEMORIAL, TOGETHER_DAYS');
                    process.exit(1);
                }
                const accountId = PAGE_ACCOUNT_MAPPING[pageName];
                const videoPath = PAGE_VIDEO_MAPPING[pageName];
                const publicId = pageName.toLowerCase();
                await uploadVideoToAccount(accountId, videoPath, publicId);
                break;
            default:
                console.log('📖 使用方法:');
                console.log('  node multi-account-upload.js test          # 测试所有账户连接');
                console.log('  node multi-account-upload.js upload        # 上传所有视频');
                console.log('  node multi-account-upload.js upload-page INDEX  # 上传单个页面视频');
                break;
        }
    } catch (error) {
        console.error('❌ 执行失败:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    uploadVideoToAccount,
    uploadAllVideos,
    testAccountConnection,
    testAllConnections
};
