#!/usr/bin/env node

/**
 * 多账户Cloudinary加载测试脚本
 * 测试每个页面是否使用了对应的账户来加载视频
 */

const https = require('https');
const http = require('http');

// 页面到账户的映射
const PAGE_ACCOUNT_MAPPING = {
    'INDEX': 'dcglebc2w',
    'ANNIVERSARY': 'drhqbbqxz',
    'MEETINGS': 'dkqnm9nwr',
    'MEMORIAL': 'ds14sv2gh',
    'TOGETHER_DAYS': 'dpq95x5nf'
};

// 页面到视频ID的映射
const PAGE_VIDEO_MAPPING = {
    'INDEX': 'index',
    'ANNIVERSARY': 'anniversary',
    'MEETINGS': 'meetings',
    'MEMORIAL': 'memorial',
    'TOGETHER_DAYS': 'together_days'
};

// 页面URL映射
const PAGE_URL_MAPPING = {
    'INDEX': 'https://love.yuh.cool/',
    'ANNIVERSARY': 'https://love.yuh.cool/anniversary',
    'MEETINGS': 'https://love.yuh.cool/meetings',
    'MEMORIAL': 'https://love.yuh.cool/memorial',
    'TOGETHER_DAYS': 'https://love.yuh.cool/together-days'
};

/**
 * 测试Cloudinary视频URL是否可访问
 */
function testCloudinaryUrl(accountId, videoId) {
    return new Promise((resolve) => {
        const url = `https://res.cloudinary.com/${accountId}/video/upload/love-website/${videoId}.mp4`;
        
        console.log(`🔍 测试URL: ${url}`);
        
        const request = https.get(url, (response) => {
            const statusCode = response.statusCode;
            const contentLength = response.headers['content-length'];
            
            if (statusCode === 200) {
                console.log(`✅ 视频可访问 - 状态码: ${statusCode}, 大小: ${(contentLength / 1024 / 1024).toFixed(2)} MB`);
                resolve({ success: true, statusCode, size: contentLength, url });
            } else {
                console.log(`❌ 视频不可访问 - 状态码: ${statusCode}`);
                resolve({ success: false, statusCode, url });
            }
            
            // 关闭响应流
            response.destroy();
        });
        
        request.on('error', (error) => {
            console.log(`❌ 请求错误: ${error.message}`);
            resolve({ success: false, error: error.message, url });
        });
        
        request.setTimeout(10000, () => {
            console.log(`⏰ 请求超时`);
            request.destroy();
            resolve({ success: false, error: 'timeout', url });
        });
    });
}

/**
 * 测试页面是否可访问
 */
function testPageUrl(pageUrl) {
    return new Promise((resolve) => {
        console.log(`🌐 测试页面: ${pageUrl}`);
        
        const request = https.get(pageUrl, (response) => {
            const statusCode = response.statusCode;
            
            if (statusCode === 200) {
                console.log(`✅ 页面可访问 - 状态码: ${statusCode}`);
                resolve({ success: true, statusCode, url: pageUrl });
            } else {
                console.log(`❌ 页面不可访问 - 状态码: ${statusCode}`);
                resolve({ success: false, statusCode, url: pageUrl });
            }
            
            response.destroy();
        });
        
        request.on('error', (error) => {
            console.log(`❌ 页面请求错误: ${error.message}`);
            resolve({ success: false, error: error.message, url: pageUrl });
        });
        
        request.setTimeout(10000, () => {
            console.log(`⏰ 页面请求超时`);
            request.destroy();
            resolve({ success: false, error: 'timeout', url: pageUrl });
        });
    });
}

/**
 * 测试单个页面的视频加载
 */
async function testPageVideoLoading(pageName) {
    console.log(`\n📋 测试页面: ${pageName}`);
    console.log('='.repeat(50));
    
    const accountId = PAGE_ACCOUNT_MAPPING[pageName];
    const videoId = PAGE_VIDEO_MAPPING[pageName];
    const pageUrl = PAGE_URL_MAPPING[pageName];
    
    console.log(`🎯 预期账户: ${accountId}`);
    console.log(`🎬 视频ID: ${videoId}`);
    console.log(`🌐 页面URL: ${pageUrl}`);
    
    // 测试页面可访问性
    const pageResult = await testPageUrl(pageUrl);
    
    // 测试视频URL可访问性
    const videoResult = await testCloudinaryUrl(accountId, videoId);
    
    const result = {
        page: pageName,
        account: accountId,
        videoId: videoId,
        pageUrl: pageUrl,
        videoUrl: videoResult.url,
        pageAccessible: pageResult.success,
        videoAccessible: videoResult.success,
        videoSize: videoResult.size,
        success: pageResult.success && videoResult.success
    };
    
    if (result.success) {
        console.log(`🎉 ${pageName} 测试通过！`);
    } else {
        console.log(`⚠️ ${pageName} 测试失败！`);
    }
    
    return result;
}

/**
 * 测试所有页面的视频加载
 */
async function testAllPages() {
    console.log('🚀 开始多账户视频加载测试...\n');
    
    const results = [];
    
    for (const pageName of Object.keys(PAGE_ACCOUNT_MAPPING)) {
        try {
            const result = await testPageVideoLoading(pageName);
            results.push(result);
            
            // 等待2秒避免请求过于频繁
            await new Promise(resolve => setTimeout(resolve, 2000));
            
        } catch (error) {
            console.error(`❌ 测试 ${pageName} 时发生错误:`, error.message);
            results.push({
                page: pageName,
                success: false,
                error: error.message
            });
        }
    }
    
    // 输出测试结果汇总
    console.log('\n📊 测试结果汇总:');
    console.log('='.repeat(80));
    
    let successCount = 0;
    let totalSize = 0;
    
    results.forEach(result => {
        if (result.success) {
            console.log(`✅ ${result.page}: 账户 ${result.account} - ${(result.videoSize / 1024 / 1024).toFixed(2)} MB`);
            console.log(`   视频URL: ${result.videoUrl}`);
            console.log(`   页面URL: ${result.pageUrl}`);
            successCount++;
            totalSize += parseInt(result.videoSize || 0);
        } else {
            console.log(`❌ ${result.page}: 测试失败`);
            if (result.error) {
                console.log(`   错误: ${result.error}`);
            }
        }
        console.log('');
    });
    
    console.log('📈 统计信息:');
    console.log(`成功测试: ${successCount}/${results.length}`);
    console.log(`总视频大小: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
    
    if (successCount === results.length) {
        console.log('\n🎉 所有页面测试通过！多账户配置工作正常！');
    } else {
        console.log('\n⚠️ 部分页面测试失败，请检查配置');
    }
    
    return results;
}

/**
 * 生成测试报告
 */
function generateTestReport(results) {
    const timestamp = new Date().toISOString();
    const reportContent = `# 多账户Cloudinary加载测试报告

**测试时间**: ${timestamp}
**测试版本**: 多账户管理模式 v1.0

## 📋 测试概况

| 页面 | 账户 | 视频ID | 状态 | 大小 |
|------|------|--------|------|------|
${results.map(r => {
    const status = r.success ? '✅ 通过' : '❌ 失败';
    const size = r.videoSize ? `${(r.videoSize / 1024 / 1024).toFixed(2)} MB` : 'N/A';
    return `| ${r.page} | ${r.account || 'N/A'} | ${r.videoId || 'N/A'} | ${status} | ${size} |`;
}).join('\n')}

## 🔗 测试URL

${results.map(r => {
    if (r.success) {
        return `### ${r.page}
- **页面URL**: ${r.pageUrl}
- **视频URL**: ${r.videoUrl}
- **账户**: ${r.account}
- **大小**: ${(r.videoSize / 1024 / 1024).toFixed(2)} MB`;
    } else {
        return `### ${r.page}
- **状态**: 测试失败
- **错误**: ${r.error || '未知错误'}`;
    }
}).join('\n\n')}

## 📊 测试结果

- **总页面数**: ${results.length}
- **成功页面数**: ${results.filter(r => r.success).length}
- **失败页面数**: ${results.filter(r => !r.success).length}
- **成功率**: ${((results.filter(r => r.success).length / results.length) * 100).toFixed(1)}%

---
**报告生成时间**: ${new Date().toLocaleString()}
`;

    const reportFile = `multi-account-test-report-${Date.now()}.md`;
    require('fs').writeFileSync(reportFile, reportContent);
    console.log(`📝 测试报告已生成: ${reportFile}`);
    
    return reportFile;
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    const command = args[0];

    try {
        switch (command) {
            case 'test-page':
                const pageName = args[1];
                if (!pageName || !PAGE_ACCOUNT_MAPPING[pageName]) {
                    console.error('❌ 请指定有效的页面名称: INDEX, ANNIVERSARY, MEETINGS, MEMORIAL, TOGETHER_DAYS');
                    process.exit(1);
                }
                await testPageVideoLoading(pageName);
                break;
            case 'test-all':
            default:
                const results = await testAllPages();
                generateTestReport(results);
                break;
        }
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    testPageVideoLoading,
    testAllPages,
    testCloudinaryUrl,
    testPageUrl
};
