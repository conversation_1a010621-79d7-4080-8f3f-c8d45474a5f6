#!/bin/bash

# 多账户Cloudinary环境变量设置脚本
# 用于安全地管理多个Cloudinary账户的API密钥

echo "🔧 设置多账户Cloudinary环境变量..."

# 主账户 (dcglebc2w) - 首页专用
export CLOUDINARY_CLOUD_NAME_PRIMARY="dcglebc2w"
export CLOUDINARY_API_KEY_DCGLEBC2W="***************"
export CLOUDINARY_API_SECRET_DCGLEBC2W="FfwmlQJX_0LOszwF6YF9KbnhmoU"

# 纪念日账户 (drhqbbqxz)
export CLOUDINARY_CLOUD_NAME_ANNIVERSARY="drhqbbqxz"
export CLOUDINARY_API_KEY_DRHQBBQXZ="***************"
export CLOUDINARY_API_SECRET_DRHQBBQXZ="7g-JSBacW-ccz1cSAdkHw_wCrU8"

# 相遇回忆账户 (dkqnm9nwr)
export CLOUDINARY_CLOUD_NAME_MEETINGS="dkqnm9nwr"
export CLOUDINARY_API_KEY_DKQNM9NWR="***************"
export CLOUDINARY_API_SECRET_DKQNM9NWR="juh_-_Amw-ds0gY03QL-E88oOIQ"

# 纪念相册账户 (ds14sv2gh)
export CLOUDINARY_CLOUD_NAME_MEMORIAL="ds14sv2gh"
export CLOUDINARY_API_KEY_DS14SV2GH="***************"
export CLOUDINARY_API_SECRET_DS14SV2GH="ajE1x9E4Ynrg5AioDxJC_EZuTow"

# 在一起的日子账户 (dpq95x5nf)
export CLOUDINARY_CLOUD_NAME_TOGETHER_DAYS="dpq95x5nf"
export CLOUDINARY_API_KEY_DPQ95X5NF="***************"
export CLOUDINARY_API_SECRET_DPQ95X5NF="849z0GBq5fapCwUCaZ0Ct0H4-5Y"

# 备用账户 (dtsgvqrna)
export CLOUDINARY_CLOUD_NAME_BACKUP="dtsgvqrna"
export CLOUDINARY_API_KEY_DTSGVQRNA="***************"
export CLOUDINARY_API_SECRET_DTSGVQRNA="wgHrEwcNyzFyOceB9Q9yAHbteqc"

# 多账户模式启用标志
export CLOUDINARY_MULTI_ACCOUNT_ENABLED="true"

# 显示设置结果
echo "✅ 环境变量设置完成！"
echo ""
echo "📋 账户配置汇总:"
echo "  主账户 (首页): $CLOUDINARY_CLOUD_NAME_PRIMARY"
echo "  纪念日: $CLOUDINARY_CLOUD_NAME_ANNIVERSARY"
echo "  相遇回忆: $CLOUDINARY_CLOUD_NAME_MEETINGS"
echo "  纪念相册: $CLOUDINARY_CLOUD_NAME_MEMORIAL"
echo "  在一起的日子: $CLOUDINARY_CLOUD_NAME_TOGETHER_DAYS"
echo "  备用账户: $CLOUDINARY_CLOUD_NAME_BACKUP"
echo ""
echo "🔒 安全提示:"
echo "  - API密钥已设置为环境变量，不会在代码中暴露"
echo "  - 建议将此脚本添加到 .bashrc 或 .zshrc 中"
echo "  - 生产环境中请使用更安全的密钥管理方案"
echo ""
echo "📖 使用方法:"
echo "  source scripts/setup-multi-account-env.sh"
echo "  node scripts/multi-account-upload.js test"
echo ""

# 创建 .env 文件（可选）
if [ "$1" = "--create-env" ]; then
    echo "📝 创建 .env 文件..."
    cat > .env << EOF
# 多账户Cloudinary配置
CLOUDINARY_MULTI_ACCOUNT_ENABLED=true

# 主账户 (dcglebc2w) - 首页专用
CLOUDINARY_CLOUD_NAME_PRIMARY=dcglebc2w
CLOUDINARY_API_KEY_DCGLEBC2W=***************
CLOUDINARY_API_SECRET_DCGLEBC2W=FfwmlQJX_0LOszwF6YF9KbnhmoU

# 纪念日账户 (drhqbbqxz)
CLOUDINARY_CLOUD_NAME_ANNIVERSARY=drhqbbqxz
CLOUDINARY_API_KEY_DRHQBBQXZ=***************
CLOUDINARY_API_SECRET_DRHQBBQXZ=7g-JSBacW-ccz1cSAdkHw_wCrU8

# 相遇回忆账户 (dkqnm9nwr)
CLOUDINARY_CLOUD_NAME_MEETINGS=dkqnm9nwr
CLOUDINARY_API_KEY_DKQNM9NWR=***************
CLOUDINARY_API_SECRET_DKQNM9NWR=juh_-_Amw-ds0gY03QL-E88oOIQ

# 纪念相册账户 (ds14sv2gh)
CLOUDINARY_CLOUD_NAME_MEMORIAL=ds14sv2gh
CLOUDINARY_API_KEY_DS14SV2GH=***************
CLOUDINARY_API_SECRET_DS14SV2GH=ajE1x9E4Ynrg5AioDxJC_EZuTow

# 在一起的日子账户 (dpq95x5nf)
CLOUDINARY_CLOUD_NAME_TOGETHER_DAYS=dpq95x5nf
CLOUDINARY_API_KEY_DPQ95X5NF=***************
CLOUDINARY_API_SECRET_DPQ95X5NF=849z0GBq5fapCwUCaZ0Ct0H4-5Y

# 备用账户 (dtsgvqrna)
CLOUDINARY_CLOUD_NAME_BACKUP=dtsgvqrna
CLOUDINARY_API_KEY_DTSGVQRNA=***************
CLOUDINARY_API_SECRET_DTSGVQRNA=wgHrEwcNyzFyOceB9Q9yAHbteqc
EOF
    echo "✅ .env 文件已创建"
    echo "⚠️  请确保 .env 文件已添加到 .gitignore 中"
fi

# 验证环境变量
echo "🔍 验证环境变量设置..."
if [ -n "$CLOUDINARY_API_KEY_DCGLEBC2W" ]; then
    echo "✅ 主账户环境变量已设置"
else
    echo "❌ 主账户环境变量设置失败"
fi

if [ -n "$CLOUDINARY_API_KEY_DRHQBBQXZ" ]; then
    echo "✅ 纪念日账户环境变量已设置"
else
    echo "❌ 纪念日账户环境变量设置失败"
fi

if [ -n "$CLOUDINARY_API_KEY_DKQNM9NWR" ]; then
    echo "✅ 相遇回忆账户环境变量已设置"
else
    echo "❌ 相遇回忆账户环境变量设置失败"
fi

if [ -n "$CLOUDINARY_API_KEY_DS14SV2GH" ]; then
    echo "✅ 纪念相册账户环境变量已设置"
else
    echo "❌ 纪念相册账户环境变量设置失败"
fi

if [ -n "$CLOUDINARY_API_KEY_DPQ95X5NF" ]; then
    echo "✅ 在一起的日子账户环境变量已设置"
else
    echo "❌ 在一起的日子账户环境变量设置失败"
fi

if [ -n "$CLOUDINARY_API_KEY_DTSGVQRNA" ]; then
    echo "✅ 备用账户环境变量已设置"
else
    echo "❌ 备用账户环境变量设置失败"
fi

echo ""
echo "🎯 下一步操作:"
echo "1. 测试账户连接: node scripts/multi-account-upload.js test"
echo "2. 上传视频到各账户: node scripts/multi-account-upload.js upload"
echo "3. 访问监控面板查看状态: https://love.yuh.cool/test/cdn-monitoring-dashboard.html"
echo ""
echo "📚 相关文档:"
echo "  - 完整部署指南: docs/Cloudinary-CDN-负载均衡完整部署指南.md"
echo "  - 使用指南: docs/guide-love.md"
