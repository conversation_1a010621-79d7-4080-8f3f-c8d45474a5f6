#!/bin/bash

# 多账户Cloudinary部署脚本
# 自动化部署和测试多账户Cloudinary配置

set -e  # 遇到错误立即退出

echo "🚀 开始部署多账户Cloudinary系统..."
echo "=================================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的文件
check_files() {
    log_info "检查必要文件..."
    
    local files=(
        "config.js"
        "hybrid-cdn-manager.js"
        "cloudinary-load-balancer.js"
        "scripts/multi-account-upload.js"
        "scripts/setup-multi-account-env.sh"
        "test/multi-account-monitoring.html"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            log_success "✅ $file 存在"
        else
            log_error "❌ $file 不存在"
            exit 1
        fi
    done
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量..."
    
    if [ -f "scripts/setup-multi-account-env.sh" ]; then
        source scripts/setup-multi-account-env.sh
        log_success "✅ 环境变量设置完成"
    else
        log_error "❌ 环境变量设置脚本不存在"
        exit 1
    fi
}

# 检查Node.js依赖
check_dependencies() {
    log_info "检查Node.js依赖..."
    
    if ! command -v node &> /dev/null; then
        log_error "❌ Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "❌ npm 未安装"
        exit 1
    fi
    
    # 检查cloudinary包
    if [ ! -d "node_modules/cloudinary" ]; then
        log_warning "⚠️ Cloudinary包未安装，正在安装..."
        npm install cloudinary
    fi
    
    log_success "✅ 依赖检查完成"
}

# 测试账户连接
test_connections() {
    log_info "测试所有账户连接..."
    
    if node scripts/multi-account-upload.js test; then
        log_success "✅ 账户连接测试通过"
    else
        log_error "❌ 账户连接测试失败"
        return 1
    fi
}

# 检查视频文件
check_videos() {
    log_info "检查视频文件..."
    
    local videos=(
        "background/home/<USER>"
        "background/anniversary/anniversary.mp4"
        "background/meetings/meetings.mp4"
        "background/memorial/memorial.mp4"
        "background/together-days/together-days.mp4"
    )
    
    local missing_videos=()
    
    for video in "${videos[@]}"; do
        if [ -f "$video" ]; then
            local size=$(du -h "$video" | cut -f1)
            log_success "✅ $video ($size)"
        else
            log_warning "⚠️ $video 不存在"
            missing_videos+=("$video")
        fi
    done
    
    if [ ${#missing_videos[@]} -gt 0 ]; then
        log_warning "发现 ${#missing_videos[@]} 个缺失的视频文件"
        echo "缺失的文件:"
        for video in "${missing_videos[@]}"; do
            echo "  - $video"
        done
        
        read -p "是否继续部署？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "部署已取消"
            exit 0
        fi
    fi
}

# 上传视频到各账户
upload_videos() {
    log_info "上传视频到各账户..."
    
    read -p "是否要上传视频到Cloudinary账户？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if node scripts/multi-account-upload.js upload; then
            log_success "✅ 视频上传完成"
        else
            log_error "❌ 视频上传失败"
            return 1
        fi
    else
        log_info "跳过视频上传"
    fi
}

# 验证配置
verify_config() {
    log_info "验证配置文件..."
    
    # 检查config.js中的多账户配置
    if grep -q "MULTI_ACCOUNT" config.js; then
        log_success "✅ 多账户配置已启用"
    else
        log_error "❌ 多账户配置未找到"
        return 1
    fi
    
    # 检查页面映射
    if grep -q "PAGE_ACCOUNT_MAPPING" config.js; then
        log_success "✅ 页面账户映射配置正确"
    else
        log_error "❌ 页面账户映射配置未找到"
        return 1
    fi
}

# 测试页面加载
test_pages() {
    log_info "测试页面加载..."
    
    local pages=(
        "https://love.yuh.cool/"
        "https://love.yuh.cool/anniversary"
        "https://love.yuh.cool/meetings"
        "https://love.yuh.cool/memorial"
        "https://love.yuh.cool/together-days"
        "https://love.yuh.cool/test/multi-account-monitoring.html"
    )
    
    for page in "${pages[@]}"; do
        log_info "测试页面: $page"
        if curl -s -o /dev/null -w "%{http_code}" "$page" | grep -q "200"; then
            log_success "✅ $page 可访问"
        else
            log_warning "⚠️ $page 可能无法访问"
        fi
    done
}

# 生成部署报告
generate_report() {
    log_info "生成部署报告..."
    
    local report_file="deployment-report-$(date +%Y%m%d-%H%M%S).md"
    
    cat > "$report_file" << EOF
# 多账户Cloudinary部署报告

**部署时间**: $(date)
**部署版本**: 多账户管理模式 v1.0

## 📋 部署概况

### ✅ 已完成项目
- [x] 多账户配置更新
- [x] 混合CDN管理器增强
- [x] 负载均衡器更新
- [x] 环境变量配置
- [x] 上传脚本创建
- [x] 监控面板部署

### 🎯 账户分配

| 页面 | 账户ID | 云名称 | 状态 |
|------|--------|--------|------|
| 首页 | dcglebc2w | dcglebc2w | ✅ 活跃 |
| 纪念日 | drhqbbqxz | drhqbbqxz | ✅ 活跃 |
| 相遇回忆 | dkqnm9nwr | dkqnm9nwr | ✅ 活跃 |
| 纪念相册 | ds14sv2gh | ds14sv2gh | ✅ 活跃 |
| 在一起的日子 | dpq95x5nf | dpq95x5nf | ✅ 活跃 |
| 备用 | dtsgvqrna | dtsgvqrna | 🟡 待机 |

### 📊 配额分配
- **总配额**: 150GB (6 × 25GB)
- **分配策略**: 每页面专用账户
- **故障转移**: 启用备用账户

### 🔗 重要链接
- **监控面板**: https://love.yuh.cool/test/multi-account-monitoring.html
- **主网站**: https://love.yuh.cool/
- **测试页面**: https://love.yuh.cool/test/

### 📝 后续操作
1. 定期检查账户配额使用情况
2. 监控视频加载性能
3. 根据需要调整负载均衡策略
4. 备份重要配置文件

### 🛠️ 维护命令
\`\`\`bash
# 测试账户连接
node scripts/multi-account-upload.js test

# 上传视频到指定账户
node scripts/multi-account-upload.js upload-page INDEX

# 查看系统状态
curl https://love.yuh.cool/test/multi-account-monitoring.html
\`\`\`

---
**报告生成时间**: $(date)
EOF

    log_success "✅ 部署报告已生成: $report_file"
}

# 主函数
main() {
    echo "🎯 多账户Cloudinary部署向导"
    echo "================================"
    
    # 检查当前目录
    if [ ! -f "config.js" ]; then
        log_error "❌ 请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行部署步骤
    check_files
    setup_environment
    check_dependencies
    verify_config
    check_videos
    
    # 可选步骤
    if test_connections; then
        log_success "✅ 连接测试通过"
        upload_videos
    else
        log_warning "⚠️ 连接测试失败，跳过视频上传"
    fi
    
    test_pages
    generate_report
    
    echo ""
    echo "🎉 多账户Cloudinary部署完成！"
    echo "================================"
    echo ""
    echo "📋 下一步操作:"
    echo "1. 访问监控面板: https://love.yuh.cool/test/multi-account-monitoring.html"
    echo "2. 测试各页面视频加载: https://love.yuh.cool/"
    echo "3. 查看部署报告: $(ls deployment-report-*.md | tail -1)"
    echo ""
    echo "📚 相关文档:"
    echo "- 完整指南: docs/Cloudinary-CDN-负载均衡完整部署指南.md"
    echo "- 使用指南: docs/guide-love.md"
    echo ""
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
