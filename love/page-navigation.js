/**
 * 页面导航优化模块
 * 实现快速页面切换、预加载和用户体验优化
 */

class PageNavigationOptimizer {
    constructor() {
        this.pageCache = new Map();
        this.preloadQueue = [];
        this.isNavigating = false;
        this.touchStartTime = 0;
        this.touchStartY = 0;
        
        this.init();
    }
    
    init() {
        this.setupBackButtonOptimization();
        this.setupPagePreloading();
        this.setupMobileOptimizations();
        this.setupNavigationAnimations();
        
        console.log('🚀 页面导航优化器已初始化');
    }
    
    /**
     * 优化返回主页按钮
     */
    setupBackButtonOptimization() {
        // 查找所有返回按钮
        const backButtons = document.querySelectorAll('.back-button, a[href="/"]');
        
        backButtons.forEach(button => {
            // 移除原有的href属性，防止默认跳转
            if (button.tagName === 'A') {
                button.removeAttribute('href');
                button.style.cursor = 'pointer';
            }
            
            // 添加优化的点击处理
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.navigateToHome();
            });
            
            // 添加触摸反馈
            this.addTouchFeedback(button);
        });
    }
    
    /**
     * 导航到主页（优化版本）
     */
    async navigateToHome() {
        if (this.isNavigating) return;
        
        this.isNavigating = true;
        
        try {
            // 显示导航加载状态
            this.showNavigationLoader();
            
            // 检查主页是否已预加载
            if (this.pageCache.has('/')) {
                console.log('✅ 使用预加载的主页内容');
                await this.loadCachedPage('/');
            } else {
                console.log('🔄 加载主页内容');
                await this.loadPageWithAnimation('/');
            }
            
        } catch (error) {
            console.error('❌ 页面导航失败:', error);
            // 回退到传统导航
            window.location.href = '/';
        } finally {
            this.isNavigating = false;
            this.hideNavigationLoader();
        }
    }
    
    /**
     * 带动画的页面加载
     */
    async loadPageWithAnimation(url) {
        return new Promise((resolve, reject) => {
            // 创建页面切换动画
            const overlay = this.createTransitionOverlay();
            document.body.appendChild(overlay);
            
            // 淡出当前页面
            document.body.style.opacity = '0.7';
            document.body.style.transform = 'scale(0.98)';
            
            setTimeout(() => {
                window.location.href = url;
                resolve();
            }, 300);
        });
    }
    
    /**
     * 加载缓存的页面
     */
    async loadCachedPage(url) {
        const cachedContent = this.pageCache.get(url);
        if (!cachedContent) {
            throw new Error('缓存内容不存在');
        }
        
        // 创建平滑过渡效果
        const overlay = this.createTransitionOverlay();
        document.body.appendChild(overlay);
        
        // 淡出当前页面
        document.body.style.opacity = '0.8';
        
        setTimeout(() => {
            // 这里可以实现更复杂的SPA式页面切换
            // 目前先使用传统导航作为回退
            window.location.href = url;
        }, 200);
    }
    
    /**
     * 创建页面切换过渡层
     */
    createTransitionOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'page-transition-overlay';
        overlay.innerHTML = `
            <div class="transition-content">
                <div class="transition-spinner"></div>
                <div class="transition-text">正在切换页面...</div>
            </div>
        `;
        
        return overlay;
    }
    
    /**
     * 显示导航加载器
     */
    showNavigationLoader() {
        let loader = document.querySelector('.navigation-loader');
        if (!loader) {
            loader = document.createElement('div');
            loader.className = 'navigation-loader';
            loader.innerHTML = `
                <div class="nav-loader-content">
                    <div class="nav-loader-spinner"></div>
                    <span>加载中...</span>
                </div>
            `;
            document.body.appendChild(loader);
        }
        
        loader.classList.add('show');
    }
    
    /**
     * 隐藏导航加载器
     */
    hideNavigationLoader() {
        const loader = document.querySelector('.navigation-loader');
        if (loader) {
            loader.classList.remove('show');
            setTimeout(() => {
                if (loader.parentNode) {
                    loader.parentNode.removeChild(loader);
                }
            }, 300);
        }
    }
    
    /**
     * 设置页面预加载
     */
    setupPagePreloading() {
        // 预加载主页内容
        if (window.location.pathname !== '/') {
            this.preloadPage('/');
        }
        
        // 预加载其他重要页面 - 修复路由问题
        const importantPages = ['/anniversary', '/meetings', '/memorial', '/together-days'];
        importantPages.forEach(page => {
            if (window.location.pathname !== page) {
                setTimeout(() => this.preloadPage(page), 2000);
            }
        });
    }
    
    /**
     * 预加载页面
     */
    async preloadPage(url) {
        if (this.pageCache.has(url)) return;
        
        try {
            const response = await fetch(url);
            if (response.ok) {
                const content = await response.text();
                this.pageCache.set(url, content);
                console.log(`✅ 预加载完成: ${url}`);
            }
        } catch (error) {
            console.warn(`⚠️ 预加载失败: ${url}`, error);
        }
    }
    
    /**
     * 添加触摸反馈
     */
    addTouchFeedback(element) {
        element.addEventListener('touchstart', (e) => {
            this.touchStartTime = Date.now();
            this.touchStartY = e.touches[0].clientY;
            
            element.style.transform = 'scale(0.95)';
            element.style.opacity = '0.8';
            element.style.transition = 'all 0.1s ease';
        });
        
        element.addEventListener('touchend', (e) => {
            const touchDuration = Date.now() - this.touchStartTime;
            const touchDistance = Math.abs(e.changedTouches[0].clientY - this.touchStartY);
            
            // 恢复原始状态
            element.style.transform = '';
            element.style.opacity = '';
            element.style.transition = 'all 0.2s ease';
            
            // 如果是快速点击且没有滑动，触发点击效果
            if (touchDuration < 200 && touchDistance < 10) {
                this.createTouchRipple(element, e.changedTouches[0]);
            }
        });
        
        element.addEventListener('touchcancel', () => {
            element.style.transform = '';
            element.style.opacity = '';
            element.style.transition = 'all 0.2s ease';
        });
    }
    
    /**
     * 创建触摸涟漪效果
     */
    createTouchRipple(element, touch) {
        const rect = element.getBoundingClientRect();
        const ripple = document.createElement('div');
        ripple.className = 'touch-ripple';
        
        const size = Math.max(rect.width, rect.height);
        const x = touch.clientX - rect.left - size / 2;
        const y = touch.clientY - rect.top - size / 2;
        
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 600);
    }
    
    /**
     * 设置移动端优化
     */
    setupMobileOptimizations() {
        // 优化移动端滚动
        if ('ontouchstart' in window) {
            document.body.style.webkitOverflowScrolling = 'touch';
            document.body.style.overflowScrolling = 'touch';
        }
        
        // 防止移动端双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', (e) => {
            const now = Date.now();
            if (now - lastTouchEnd <= 300) {
                e.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // 优化移动端点击延迟
        document.addEventListener('touchstart', () => {}, { passive: true });
    }
    
    /**
     * 设置导航动画
     */
    setupNavigationAnimations() {
        // 页面加载完成后的入场动画
        document.addEventListener('DOMContentLoaded', () => {
            document.body.style.opacity = '0';
            document.body.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                document.body.style.transition = 'all 0.5s ease';
                document.body.style.opacity = '1';
                document.body.style.transform = 'translateY(0)';
            }, 100);
        });
    }
}

// 自动初始化
if (typeof window !== 'undefined') {
    window.pageNavigationOptimizer = new PageNavigationOptimizer();
}
