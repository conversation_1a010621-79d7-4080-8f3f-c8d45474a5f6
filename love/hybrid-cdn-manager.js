/**
 * 混合CDN管理器
 * 结合Cloudinary + 本地文件 + 其他CDN的智能负载均衡
 */

class HybridCDNManager {
    constructor() {
        // 初始化多账户Cloudinary源配置
        this.sources = this.initializeCloudinarySources();

        // 添加本地文件源作为备选
        this.sources.push(
            {
                id: 'local-optimized',
                type: 'local',
                baseUrl: '/background/cloudinary-ready',
                priority: 10, // 降低优先级，作为最后备选
                quota: Infinity, // 无限制
                used: 0,
                status: 'active'
            },
            {
                id: 'local-original',
                type: 'local',
                baseUrl: '/background',
                priority: 11,
                quota: Infinity,
                used: 0,
                status: 'fallback' // 最后备选
            }
        );

        // 视频文件映射 - 修复页面键匹配问题
        this.videoMappings = {
            // 使用大写键匹配simple-video-manager.js中的getCurrentPageKey()
            'INDEX': {
                cloudinary: 'love-website/home',
                local: 'home/home.mp4',
                localOptimized: 'home.mp4'
            },
            'MEETINGS': {
                cloudinary: 'love-website/meetings',
                local: 'meetings/meetings.mp4',
                localOptimized: 'meetings.mp4'
            },
            'ANNIVERSARY': {
                cloudinary: 'love-website/anniversary',
                local: 'anniversary/anniversary.mp4',
                localOptimized: 'anniversary.mp4'
            },
            'MEMORIAL': {
                cloudinary: 'love-website/memorial',
                local: 'memorial/memorial.mp4',
                localOptimized: 'memorial.mp4'
            },
            'TOGETHER_DAYS': {
                cloudinary: 'love-website/together-days',
                local: 'together-days/together-days.mp4',
                localOptimized: 'together-days.mp4'
            },
            // 保持向后兼容的小写键
            'home': {
                cloudinary: 'love-website/home',
                local: 'home/home.mp4',
                localOptimized: 'home.mp4'
            },
            'meetings': {
                cloudinary: 'love-website/meetings',
                local: 'meetings/meetings.mp4',
                localOptimized: 'meetings.mp4'
            },
            'anniversary': {
                cloudinary: 'love-website/anniversary',
                local: 'anniversary/anniversary.mp4',
                localOptimized: 'anniversary.mp4'
            },
            'together-days': {
                cloudinary: 'love-website/together-days',
                local: 'together-days/together-days.mp4',
                localOptimized: 'together-days.mp4'
            },
            'memorial': {
                cloudinary: 'love-website/memorial',
                local: 'memorial/memorial.mp4',
                localOptimized: 'memorial.mp4'
            }
        };

        // 智能选择策略
        this.strategy = 'page-dedicated'; // 'page-dedicated', 'smart', 'quota-aware', 'performance'
        this.performanceStats = new Map();
        this.accountHealthStatus = new Map(); // 账户健康状态缓存
    }

    // 初始化多账户Cloudinary源配置
    initializeCloudinarySources() {
        const sources = [];

        // 检查是否启用多账户模式
        if (window.CONFIG && window.CONFIG.CLOUDINARY && window.CONFIG.CLOUDINARY.MULTI_ACCOUNT && window.CONFIG.CLOUDINARY.MULTI_ACCOUNT.ENABLED) {
            const accounts = window.CONFIG.CLOUDINARY.MULTI_ACCOUNT.ACCOUNTS;

            // 为每个账户创建源配置
            Object.entries(accounts).forEach(([accountId, config]) => {
                sources.push({
                    id: `cloudinary-${accountId}`,
                    type: 'cloudinary',
                    accountId: accountId,
                    cloudName: config.cloudName,
                    baseUrl: config.baseUrl,
                    priority: config.priority,
                    quota: config.quota,
                    used: 0,
                    status: config.status,
                    assignedPages: config.assignedPages || [],
                    description: config.description || `Cloudinary账户: ${accountId}`
                });
            });

            console.log(`🎯 初始化了 ${sources.length} 个Cloudinary账户源`);
        } else {
            // 向后兼容：使用单账户配置
            sources.push({
                id: 'cloudinary-primary',
                type: 'cloudinary',
                accountId: 'dcglebc2w',
                cloudName: 'dcglebc2w',
                baseUrl: 'https://res.cloudinary.com/dcglebc2w',
                priority: 1,
                quota: 25,
                used: 0,
                status: 'active',
                assignedPages: ['INDEX'],
                description: '主Cloudinary账户'
            });

            console.log('📝 使用单账户兼容模式');
        }

        return sources;
    }

    // 智能选择最佳CDN源 - 支持页面级账户选择
    selectBestSource(pageName, userContext = {}) {
        // 首先尝试获取页面专用的Cloudinary账户
        const dedicatedSource = this.getPageDedicatedSource(pageName);
        if (dedicatedSource && this.isSourceHealthy(dedicatedSource)) {
            console.log(`🎯 选择页面专用CDN源: ${dedicatedSource.id} (${dedicatedSource.accountId})`);
            return dedicatedSource;
        }

        // 如果专用账户不可用，使用故障转移逻辑
        const fallbackSource = this.selectFallbackSource(pageName, userContext);
        if (fallbackSource) {
            console.log(`⚠️ 使用故障转移CDN源: ${fallbackSource.id} (${fallbackSource.type})`);
            return fallbackSource;
        }

        // 最后使用本地文件作为备选
        const localSource = this.sources.find(s => s.type === 'local' && s.status === 'active');
        if (localSource) {
            console.log(`🔄 回退到本地文件源: ${localSource.id}`);
            return localSource;
        }

        console.error('❌ 没有可用的CDN源');
        return null;
    }

    // 获取页面专用的Cloudinary源
    getPageDedicatedSource(pageName) {
        // 检查多账户配置
        if (window.CONFIG && window.CONFIG.CLOUDINARY && window.CONFIG.CLOUDINARY.MULTI_ACCOUNT && window.CONFIG.CLOUDINARY.MULTI_ACCOUNT.ENABLED) {
            const mapping = window.CONFIG.CLOUDINARY.MULTI_ACCOUNT.PAGE_ACCOUNT_MAPPING;
            const accountId = mapping[pageName];

            if (accountId) {
                return this.sources.find(s =>
                    s.type === 'cloudinary' && s.accountId === accountId
                );
            }
        }

        // 向后兼容：返回主账户
        return this.sources.find(s => s.type === 'cloudinary' && s.id === 'cloudinary-primary');
    }

    // 选择故障转移源
    selectFallbackSource(pageName, userContext = {}) {
        const availableSources = this.sources.filter(s =>
            (s.status === 'active' || s.status === 'standby') &&
            this.isSourceHealthy(s)
        );

        // 优先选择其他可用的Cloudinary账户
        const cloudinarySources = availableSources.filter(s => s.type === 'cloudinary');
        if (cloudinarySources.length > 0) {
            // 按优先级和配额使用情况排序
            cloudinarySources.sort((a, b) => {
                // 1. 检查配额使用情况
                const aQuotaUsage = a.used / a.quota;
                const bQuotaUsage = b.used / b.quota;

                if (aQuotaUsage < 0.9 && bQuotaUsage >= 0.9) return -1;
                if (bQuotaUsage < 0.9 && aQuotaUsage >= 0.9) return 1;

                // 2. 按优先级排序
                return a.priority - b.priority;
            });

            return cloudinarySources[0];
        }

        // 如果没有可用的Cloudinary源，返回本地源
        return availableSources.find(s => s.type === 'local');
    }

    // 检查源是否健康
    isSourceHealthy(source) {
        if (source.type === 'local') {
            return true; // 本地文件总是健康的
        }

        // 检查Cloudinary账户健康状态
        const healthStatus = this.accountHealthStatus.get(source.accountId);
        if (!healthStatus) {
            return true; // 如果没有健康状态记录，假设是健康的
        }

        // 检查是否超过配额
        if (source.used >= source.quota) {
            return false;
        }

        // 检查错误率
        const errorRate = healthStatus.errorCount / (healthStatus.totalRequests || 1);
        return errorRate < 0.1; // 错误率低于10%认为是健康的
    }

    // 生成视频URL - 支持多账户
    generateVideoUrl(pageName, source, userContext = {}) {
        const mapping = this.videoMappings[pageName];
        if (!mapping) {
            console.error(`未找到页面 ${pageName} 的视频映射`);
            return null;
        }

        let url;
        switch (source.type) {
            case 'cloudinary':
                const transforms = this.getCloudinaryTransforms(userContext);
                // 使用源的baseUrl，支持多账户
                url = `${source.baseUrl}/video/upload/${transforms}/${mapping.cloudinary}.mp4`;

                // 记录使用统计（用于配额管理）
                this.recordUsage(source.accountId, 'video_request');
                break;

            case 'local':
                if (source.id === 'local-optimized') {
                    // 使用压缩后的文件
                    url = `${source.baseUrl}/${mapping.localOptimized}`;
                } else if (source.id === 'local-original') {
                    // 只有在最后备选时才使用原始文件
                    url = `${source.baseUrl}/${mapping.local}`;
                } else {
                    // 默认优先使用压缩文件
                    url = `/background/cloudinary-ready/${mapping.localOptimized}`;
                }
                break;

            default:
                console.error(`未知的源类型: ${source.type}`);
                return null;
        }

        console.log(`📹 生成视频URL: ${url} (源: ${source.id})`);
        return url;
    }

    // 记录使用统计
    recordUsage(accountId, type, size = 0) {
        if (!accountId) return;

        const now = Date.now();
        const key = `${accountId}_${type}`;

        if (!this.usageStats) {
            this.usageStats = new Map();
        }

        const stats = this.usageStats.get(key) || {
            count: 0,
            totalSize: 0,
            lastUpdate: now
        };

        stats.count++;
        stats.totalSize += size;
        stats.lastUpdate = now;

        this.usageStats.set(key, stats);

        // 更新源的使用量（简化估算）
        const source = this.sources.find(s => s.accountId === accountId);
        if (source && type === 'video_request') {
            // 估算每次视频请求约50MB
            source.used += 0.05; // GB
        }
    }

    // 获取Cloudinary变换参数 - 强制2K无损质量
    getCloudinaryTransforms(userContext) {
        const { isMobile, isTablet, isSlowConnection } = this.analyzeUserContext(userContext);

        // 强制使用配置文件中的高质量设置
        const config = window.CONFIG?.CLOUDINARY?.TRANSFORMATIONS;
        if (!config) {
            console.warn('⚠️ 未找到Cloudinary配置，使用默认2K设置');
            return 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache';
        }

        // 只有在网络极差的情况下才降级
        if (isSlowConnection && navigator.connection?.effectiveType === 'slow-2g') {
            console.log('📱 检测到极慢网络，使用降级质量');
            return config.FALLBACK || 'q_90,f_auto,w_1280,h_720,c_limit';
        }

        // 移动设备使用高清质量
        if (isMobile) {
            console.log('📱 移动设备使用高清质量');
            return config.MOBILE_HD || 'q_95,f_auto,w_1920,h_1080,c_limit,fl_immutable_cache';
        }

        // 平板设备使用2K质量
        if (isTablet) {
            console.log('📱 平板设备使用2K质量');
            return config.DESKTOP_2K || 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache';
        }

        // 桌面设备默认使用2K无损质量
        console.log('🖥️ 桌面设备使用2K无损质量');
        return config.DESKTOP_2K || 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache';
    }

    // 分析用户上下文 - 更保守的网络检测
    analyzeUserContext(context = {}) {
        // 更严格的慢网络检测，只有在真正慢的情况下才降级
        const connection = navigator.connection;
        const isReallySlowConnection = connection && (
            connection.effectiveType === 'slow-2g' ||
            (connection.effectiveType === '2g' && connection.downlink < 0.5)
        );

        return {
            isMobile: context.isMobile || window.innerWidth <= 768,
            isTablet: context.isTablet || (window.innerWidth > 768 && window.innerWidth <= 1024),
            isSlowConnection: context.isSlowConnection || isReallySlowConnection,
            userAgent: context.userAgent || navigator.userAgent
        };
    }

    // 智能加载视频（多源故障转移）
    async loadVideoBackground(pageName, videoElement, userContext = {}) {
        const maxAttempts = Math.min(this.sources.length, 3); // 最多尝试3个源
        let attempts = 0;

        while (attempts < maxAttempts) {
            const source = this.selectBestSource(pageName, userContext);
            if (!source) break;

            const startTime = performance.now();
            
            try {
                const videoUrl = this.generateVideoUrl(pageName, source, userContext);
                if (!videoUrl) {
                    attempts++;
                    continue;
                }

                console.log(`🎬 尝试加载: ${pageName} from ${source.id}`);
                console.log(`📹 URL: ${videoUrl}`);

                // 设置加载超时
                const loadPromise = new Promise((resolve, reject) => {
                    const onLoad = () => {
                        videoElement.removeEventListener('error', onError);
                        resolve();
                    };
                    const onError = (e) => {
                        videoElement.removeEventListener('canplaythrough', onLoad);
                        reject(new Error(`视频加载错误: ${e.message || 'Unknown error'}`));
                    };
                    
                    videoElement.addEventListener('canplaythrough', onLoad, { once: true });
                    videoElement.addEventListener('error', onError, { once: true });
                    
                    // 超时处理
                    setTimeout(() => {
                        videoElement.removeEventListener('canplaythrough', onLoad);
                        videoElement.removeEventListener('error', onError);
                        reject(new Error('加载超时'));
                    }, 15000);
                });

                videoElement.src = videoUrl;
                videoElement.load();

                await loadPromise;

                // 成功加载
                const loadTime = performance.now() - startTime;
                this.recordPerformance(source.id, loadTime, true);
                
                // 更新使用量（估算）
                if (source.type === 'cloudinary') {
                    source.used += 0.1; // 估算每次加载消耗0.1GB
                }

                videoElement.style.opacity = '1';
                videoElement.play().catch(e => {
                    console.log('自动播放被阻止，等待用户交互');
                });

                console.log(`✅ 视频加载成功: ${pageName} from ${source.id} (${loadTime.toFixed(0)}ms)`);
                return { success: true, source: source.id, loadTime };

            } catch (error) {
                const loadTime = performance.now() - startTime;
                this.recordPerformance(source.id, loadTime, false);
                
                console.error(`❌ 源 ${source.id} 加载失败:`, error.message);

                // 记录失败，但不修改优先级，让下次重试时仍然优先Cloudinary
                console.log(`🔄 将尝试下一个源 (尝试 ${attempts + 1}/${maxAttempts})`);

                attempts++;
            }
        }

        // 所有源都失败
        console.error(`❌ 所有CDN源都失败，回退到渐变背景`);
        this.fallbackToGradient(videoElement);
        return { success: false, source: null, error: '所有源都失败' };
    }

    // 记录性能统计 - 增强版，支持账户健康监控
    recordPerformance(sourceId, loadTime, success, error = null) {
        // 更新传统性能统计
        if (!this.performanceStats.has(sourceId)) {
            this.performanceStats.set(sourceId, {
                totalAttempts: 0,
                successCount: 0,
                totalLoadTime: 0,
                avgLoadTime: 0,
                successRate: 0
            });
        }

        const stats = this.performanceStats.get(sourceId);
        stats.totalAttempts++;
        if (success) {
            stats.successCount++;
            stats.totalLoadTime += loadTime;
        }

        stats.avgLoadTime = stats.totalLoadTime / stats.successCount || 0;
        stats.successRate = stats.successCount / stats.totalAttempts;

        // 更新账户健康状态（仅对Cloudinary账户）
        const source = this.sources.find(s => s.id === sourceId);
        if (source && source.type === 'cloudinary' && source.accountId) {
            this.updateAccountHealth(source.accountId, success, loadTime, error);
        }

        console.log(`📊 ${sourceId} 性能统计:`, {
            成功率: `${(stats.successRate * 100).toFixed(1)}%`,
            平均加载时间: `${stats.avgLoadTime.toFixed(0)}ms`,
            总尝试次数: stats.totalAttempts
        });
    }

    // 更新账户健康状态
    updateAccountHealth(accountId, success, loadTime = 0, error = null) {
        if (!this.accountHealthStatus.has(accountId)) {
            this.accountHealthStatus.set(accountId, {
                totalRequests: 0,
                successCount: 0,
                errorCount: 0,
                avgLoadTime: 0,
                lastCheck: Date.now(),
                errors: []
            });
        }

        const health = this.accountHealthStatus.get(accountId);
        health.totalRequests++;

        if (success) {
            health.successCount++;
            // 更新平均加载时间
            health.avgLoadTime = (health.avgLoadTime * (health.successCount - 1) + loadTime) / health.successCount;
        } else {
            health.errorCount++;
            if (error) {
                health.errors.push({
                    timestamp: Date.now(),
                    error: error.toString()
                });
                // 只保留最近10个错误
                if (health.errors.length > 10) {
                    health.errors = health.errors.slice(-10);
                }
            }
        }

        health.lastCheck = Date.now();

        // 如果错误率过高，暂时禁用账户
        const errorRate = health.errorCount / health.totalRequests;
        if (errorRate > 0.5 && health.totalRequests > 5) {
            const source = this.sources.find(s => s.accountId === accountId);
            if (source && source.status === 'active') {
                source.status = 'error';
                console.warn(`⚠️ 账户 ${accountId} 错误率过高 (${(errorRate * 100).toFixed(1)}%)，暂时禁用`);

                // 5分钟后重新启用
                setTimeout(() => {
                    if (source.status === 'error') {
                        source.status = 'active';
                        console.log(`✅ 账户 ${accountId} 重新启用`);
                    }
                }, 300000);
            }
        }
    }

    // 失败时回退到渐变背景
    fallbackToGradient(videoElement) {
        const container = videoElement.parentElement;
        if (container) {
            container.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            videoElement.style.display = 'none';
        }
    }

    // 获取系统状态 - 增强版多账户状态
    getSystemStatus() {
        const status = {
            timestamp: Date.now(),
            strategy: this.strategy,
            sources: [],
            summary: {
                totalAccounts: 0,
                activeAccounts: 0,
                totalQuota: 0,
                usedQuota: 0,
                healthySources: 0,
                overallUsagePercent: 0
            },
            accountHealth: {},
            performance: Object.fromEntries(this.performanceStats)
        };

        this.sources.forEach(source => {
            const sourceStatus = {
                id: source.id,
                type: source.type,
                status: source.status,
                priority: source.priority
            };

            if (source.type === 'cloudinary') {
                sourceStatus.accountId = source.accountId;
                sourceStatus.cloudName = source.cloudName;
                sourceStatus.quota = source.quota;
                sourceStatus.used = source.used;
                sourceStatus.usagePercent = ((source.used / source.quota) * 100).toFixed(1);
                sourceStatus.assignedPages = source.assignedPages || [];
                sourceStatus.description = source.description;

                // 添加健康状态信息
                const health = this.accountHealthStatus.get(source.accountId);
                if (health) {
                    sourceStatus.health = {
                        totalRequests: health.totalRequests,
                        successRate: health.totalRequests > 0 ?
                            ((health.successCount / health.totalRequests) * 100).toFixed(1) : '0',
                        avgLoadTime: health.avgLoadTime.toFixed(0),
                        lastCheck: new Date(health.lastCheck).toLocaleString(),
                        recentErrors: health.errors.slice(-3).map(e => ({
                            time: new Date(e.timestamp).toLocaleString(),
                            error: e.error
                        }))
                    };

                    // 添加到账户健康汇总
                    status.accountHealth[source.accountId] = sourceStatus.health;
                }

                // 更新汇总统计
                status.summary.totalAccounts++;
                if (source.status === 'active') status.summary.activeAccounts++;
                status.summary.totalQuota += source.quota;
                status.summary.usedQuota += source.used;
            } else {
                // 本地文件源
                sourceStatus.quota = source.quota;
                sourceStatus.used = source.used;
                sourceStatus.usage = source.quota === Infinity ? '无限制' :
                    (source.used / source.quota * 100).toFixed(1) + '%';
            }

            if (this.isSourceHealthy(source)) {
                status.summary.healthySources++;
            }

            status.sources.push(sourceStatus);
        });

        // 计算总体使用率
        status.summary.overallUsagePercent = status.summary.totalQuota > 0 ?
            ((status.summary.usedQuota / status.summary.totalQuota) * 100).toFixed(1) : '0';

        // 添加页面映射信息
        if (window.CONFIG && window.CONFIG.CLOUDINARY && window.CONFIG.CLOUDINARY.MULTI_ACCOUNT) {
            status.pageMapping = window.CONFIG.CLOUDINARY.MULTI_ACCOUNT.PAGE_ACCOUNT_MAPPING;
            status.multiAccountEnabled = window.CONFIG.CLOUDINARY.MULTI_ACCOUNT.ENABLED;
        }

        return status;
    }
}

// 全局初始化
window.HybridCDNManager = HybridCDNManager;
