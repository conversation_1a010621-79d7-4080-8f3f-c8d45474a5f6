/**
 * Cloudinary多API负载均衡管理器
 * 支持多个Cloudinary账号，智能分发请求
 */

class CloudinaryLoadBalancer {
    constructor() {
        // 从配置文件初始化多账户提供商
        this.providers = this.initializeProviders();

        // 负载均衡策略
        this.strategy = 'page-dedicated'; // 'page-dedicated', 'round-robin', 'weighted', 'failover'
        this.currentIndex = 0;
    }

    // 初始化提供商配置
    initializeProviders() {
        const providers = [];

        // 检查是否启用多账户模式
        if (window.CONFIG && window.CONFIG.CLOUDINARY && window.CONFIG.CLOUDINARY.MULTI_ACCOUNT && window.CONFIG.CLOUDINARY.MULTI_ACCOUNT.ENABLED) {
            const accounts = window.CONFIG.CLOUDINARY.MULTI_ACCOUNT.ACCOUNTS;

            // 为每个账户创建提供商配置
            Object.entries(accounts).forEach(([accountId, config]) => {
                providers.push({
                    id: accountId,
                    cloudName: config.cloudName,
                    baseUrl: config.baseUrl,
                    weight: config.status === 'active' ? 100 : 0, // 活跃账户权重100，其他为0
                    status: config.status,
                    quota: config.quota,
                    used: 0,
                    assignedPages: config.assignedPages || [],
                    priority: config.priority,
                    errorCount: 0,
                    lastError: null,
                    description: config.description
                });
            });

            console.log(`🎯 初始化了 ${providers.length} 个Cloudinary提供商`);
        } else {
            // 向后兼容：使用单账户配置
            providers.push({
                id: 'primary',
                cloudName: 'dcglebc2w',
                baseUrl: 'https://res.cloudinary.com/dcglebc2w',
                weight: 100,
                status: 'active',
                quota: 25,
                used: 0,
                assignedPages: ['INDEX'],
                priority: 1,
                errorCount: 0,
                lastError: null,
                description: '主Cloudinary账户'
            });

            console.log('📝 使用单账户兼容模式');
        }

        return providers;
    }

        // 视频配置映射 - 修复页面键匹配问题
        this.videoConfigs = {
            // 使用大写键匹配simple-video-manager.js中的getCurrentPageKey()
            'INDEX': {
                publicId: 'love-website/home',
                transformations: 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache'
            },
            'MEETINGS': {
                publicId: 'love-website/meetings',
                transformations: 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache'
            },
            'ANNIVERSARY': {
                publicId: 'love-website/anniversary',
                transformations: 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache'
            },
            'MEMORIAL': {
                publicId: 'love-website/memorial',
                transformations: 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache'
            },
            'TOGETHER_DAYS': {
                publicId: 'love-website/together-days',
                transformations: 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache'
            },
            // 保持向后兼容的小写键
            'home': {
                publicId: 'love-website/home',
                transformations: 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache'
            },
            'meetings': {
                publicId: 'love-website/meetings',
                transformations: 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache'
            },
            'anniversary': {
                publicId: 'love-website/anniversary',
                transformations: 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache'
            },
            'together-days': {
                publicId: 'love-website/together-days',
                transformations: 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache'
            },
            'memorial': {
                publicId: 'love-website/memorial',
                transformations: 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache'
            }
        };

    // 获取可用的提供商
    getAvailableProviders() {
        return this.providers.filter(p => p.status === 'active' && p.errorCount < 5);
    }

    // 选择提供商 - 支持页面级选择
    selectProvider(pageName = null) {
        // 页面专用策略：优先选择分配给该页面的账户
        if (this.strategy === 'page-dedicated' && pageName) {
            const dedicatedProvider = this.getPageDedicatedProvider(pageName);
            if (dedicatedProvider && this.isProviderHealthy(dedicatedProvider)) {
                console.log(`🎯 选择页面专用提供商: ${dedicatedProvider.id} for ${pageName}`);
                return dedicatedProvider;
            }
        }

        // 如果专用提供商不可用，使用故障转移逻辑
        const available = this.getAvailableProviders();
        if (available.length === 0) {
            console.error('❌ 没有可用的Cloudinary提供商');
            return null;
        }

        let selected;
        switch (this.strategy) {
            case 'page-dedicated':
                // 页面专用模式下的故障转移：选择其他可用账户
                selected = this.selectFallbackProvider(pageName, available);
                break;

            case 'round-robin':
                selected = available[this.currentIndex % available.length];
                this.currentIndex++;
                break;
            
            case 'weighted':
                selected = this.selectByWeight(available);
                break;
                
            case 'failover':
                selected = available[0]; // 总是选择第一个可用的
                break;
                
            default:
                selected = available[0];
        }

        console.log(`🔄 选择提供商: ${selected.id} (${selected.cloudName})`);
        return selected;
    }

    // 按权重选择
    selectByWeight(providers) {
        const totalWeight = providers.reduce((sum, p) => sum + p.weight, 0);
        let random = Math.random() * totalWeight;
        
        for (const provider of providers) {
            random -= provider.weight;
            if (random <= 0) {
                return provider;
            }
        }
        return providers[0];
    }

    // 获取页面专用的提供商
    getPageDedicatedProvider(pageName) {
        // 检查多账户配置
        if (window.CONFIG && window.CONFIG.CLOUDINARY && window.CONFIG.CLOUDINARY.MULTI_ACCOUNT && window.CONFIG.CLOUDINARY.MULTI_ACCOUNT.ENABLED) {
            const mapping = window.CONFIG.CLOUDINARY.MULTI_ACCOUNT.PAGE_ACCOUNT_MAPPING;
            const accountId = mapping[pageName];

            if (accountId) {
                return this.providers.find(p => p.id === accountId);
            }
        }

        // 向后兼容：返回主提供商
        return this.providers.find(p => p.id === 'primary');
    }

    // 选择故障转移提供商
    selectFallbackProvider(pageName, availableProviders) {
        // 排除当前页面的专用提供商（因为它已经失败了）
        const dedicatedProvider = this.getPageDedicatedProvider(pageName);
        const fallbackProviders = availableProviders.filter(p => p.id !== dedicatedProvider?.id);

        if (fallbackProviders.length === 0) {
            return availableProviders[0]; // 如果没有其他选择，还是返回专用提供商
        }

        // 按优先级和健康状态排序
        fallbackProviders.sort((a, b) => {
            // 1. 优先选择备用账户
            if (a.status === 'standby' && b.status !== 'standby') return -1;
            if (b.status === 'standby' && a.status !== 'standby') return 1;

            // 2. 按错误计数排序
            if (a.errorCount !== b.errorCount) return a.errorCount - b.errorCount;

            // 3. 按优先级排序
            return a.priority - b.priority;
        });

        return fallbackProviders[0];
    }

    // 检查提供商是否健康
    isProviderHealthy(provider) {
        // 检查错误计数
        if (provider.errorCount >= 5) {
            return false;
        }

        // 检查配额使用情况
        if (provider.quota && provider.used >= provider.quota * 0.95) {
            return false;
        }

        // 检查最近错误时间
        if (provider.lastError && (Date.now() - provider.lastError) < 300000) { // 5分钟内有错误
            return false;
        }

        return true;
    }

    // 生成视频URL - 支持页面级提供商选择
    generateVideoUrl(pageName, provider = null) {
        const config = this.videoConfigs[pageName];
        if (!config) {
            console.error(`未找到页面 ${pageName} 的视频配置`);
            return null;
        }

        // 如果没有指定提供商，根据页面自动选择
        if (!provider) {
            provider = this.selectProvider(pageName);
            if (!provider) return null;
        }

        // 设备优化
        const deviceTransforms = this.getDeviceOptimizations();
        const finalTransforms = `${config.transformations},${deviceTransforms}`;

        const url = `${provider.baseUrl}/video/upload/${finalTransforms}/${config.publicId}.mp4`;

        // 记录使用统计
        this.recordUsage(provider.id, 'video_request');

        console.log(`📹 生成视频URL: ${url} (提供商: ${provider.id})`);
        return url;
    }

    // 记录使用统计
    recordUsage(providerId, type, size = 0) {
        const provider = this.providers.find(p => p.id === providerId);
        if (!provider) return;

        // 估算每次视频请求约50MB
        if (type === 'video_request') {
            provider.used += 0.05; // GB
        }

        console.log(`📊 ${providerId} 使用统计: ${provider.used.toFixed(2)}GB / ${provider.quota}GB`);
    }

    // 设备优化策略 - 强制高质量
    getDeviceOptimizations() {
        const isMobile = window.innerWidth <= 768;
        const isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;

        // 更严格的慢网络检测
        const connection = navigator.connection;
        const isReallySlowConnection = connection && (
            connection.effectiveType === 'slow-2g' ||
            (connection.effectiveType === '2g' && connection.downlink < 0.5)
        );

        // 只有在网络极差时才降级
        if (isReallySlowConnection) {
            console.log('📱 检测到极慢网络，使用降级质量');
            return 'q_90,w_1280,h_720';
        }

        // 移动设备使用高清质量
        if (isMobile) {
            console.log('📱 移动设备使用高清质量');
            return 'q_95,w_1920,h_1080';
        }

        // 平板设备使用2K质量
        if (isTablet) {
            console.log('📱 平板设备使用2K质量');
            return 'q_100,w_2560,h_1440';
        }

        // 桌面设备使用2K无损质量
        console.log('🖥️ 桌面设备使用2K无损质量');
        return 'q_100,w_2560,h_1440';
    }

    // 智能加载视频（带故障转移）
    async loadVideoBackground(pageName, videoElement) {
        const maxRetries = this.getAvailableProviders().length;
        let attempts = 0;

        while (attempts < maxRetries) {
            const provider = this.selectProvider();
            if (!provider) break;

            try {
                const videoUrl = this.generateVideoUrl(pageName, provider);
                if (!videoUrl) continue;

                console.log(`🎬 尝试加载视频: ${pageName} from ${provider.id}`);
                
                // 设置加载超时
                const loadPromise = new Promise((resolve, reject) => {
                    videoElement.addEventListener('canplaythrough', resolve, { once: true });
                    videoElement.addEventListener('error', reject, { once: true });
                    setTimeout(() => reject(new Error('加载超时')), 10000);
                });

                videoElement.src = videoUrl;
                videoElement.load();

                await loadPromise;

                // 成功加载
                videoElement.style.opacity = '1';
                videoElement.play().catch(e => {
                    console.log('自动播放被阻止，等待用户交互');
                });

                // 重置错误计数
                provider.errorCount = 0;
                console.log(`✅ 视频加载成功: ${pageName} from ${provider.id}`);
                return true;

            } catch (error) {
                console.error(`❌ 提供商 ${provider.id} 加载失败:`, error.message);
                
                // 记录错误
                provider.errorCount++;
                provider.lastError = new Date();
                
                // 如果错误次数过多，暂时禁用
                if (provider.errorCount >= 3) {
                    provider.status = 'disabled';
                    console.warn(`⚠️ 提供商 ${provider.id} 已被暂时禁用`);
                    
                    // 5分钟后重新启用
                    setTimeout(() => {
                        provider.status = 'active';
                        provider.errorCount = 0;
                        console.log(`🔄 提供商 ${provider.id} 已重新启用`);
                    }, 5 * 60 * 1000);
                }
                
                attempts++;
            }
        }

        // 所有提供商都失败，回退到渐变背景
        console.error(`❌ 所有Cloudinary提供商都失败，回退到渐变背景`);
        this.fallbackToGradient(videoElement);
        return false;
    }

    // 失败时回退到渐变背景
    fallbackToGradient(videoElement) {
        const container = videoElement.parentElement;
        if (container) {
            container.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            videoElement.style.display = 'none';
        }
    }

    // 获取负载均衡状态
    getStatus() {
        return {
            strategy: this.strategy,
            providers: this.providers.map(p => ({
                id: p.id,
                status: p.status,
                errorCount: p.errorCount,
                weight: p.weight
            })),
            currentIndex: this.currentIndex
        };
    }
}

// 全局初始化
window.CloudinaryLoadBalancer = CloudinaryLoadBalancer;
