<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Love Website Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .debug-section {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Love Website 调试页面</h1>
    
    <div class="debug-section">
        <h2>基础检查</h2>
        <div id="basic-checks"></div>
    </div>
    
    <div class="debug-section">
        <h2>配置文件检查</h2>
        <div id="config-checks"></div>
    </div>
    
    <div class="debug-section">
        <h2>JavaScript文件加载检查</h2>
        <div id="js-checks"></div>
    </div>
    
    <div class="debug-section">
        <h2>视频管理器检查</h2>
        <div id="video-checks"></div>
    </div>
    
    <div class="debug-section">
        <h2>网络连接检查</h2>
        <div id="network-checks"></div>
    </div>
    
    <div class="debug-section">
        <h2>控制台日志</h2>
        <div id="console-logs"></div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="/config.js"></script>
    <script src="/dynamic-styles.js"></script>
    <script src="/cloudinary-setup.js"></script>
    <script src="/hybrid-cdn-manager.js"></script>
    <script src="/cloudinary-load-balancer.js"></script>
    <script src="/simple-video-manager.js"></script>
    <script src="/preload-manager.js"></script>

    <script>
        // 捕获所有控制台输出
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const logs = [];
        
        console.log = function(...args) {
            logs.push({type: 'log', message: args.join(' '), time: new Date().toLocaleTimeString()});
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            logs.push({type: 'error', message: args.join(' '), time: new Date().toLocaleTimeString()});
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            logs.push({type: 'warn', message: args.join(' '), time: new Date().toLocaleTimeString()});
            originalWarn.apply(console, args);
        };

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
        }

        function runDiagnostics() {
            // 基础检查
            addResult('basic-checks', `用户代理: ${navigator.userAgent}`, 'info');
            addResult('basic-checks', `当前URL: ${window.location.href}`, 'info');
            addResult('basic-checks', `页面标题: ${document.title}`, 'info');
            
            // 配置文件检查
            if (typeof window.CONFIG !== 'undefined') {
                addResult('config-checks', '✅ CONFIG对象已加载', 'success');
                addResult('config-checks', `Cloudinary启用: ${window.CONFIG.CLOUDINARY?.ENABLED}`, 'info');
                addResult('config-checks', `域名配置: ${window.CONFIG.DOMAIN?.current}`, 'info');
            } else {
                addResult('config-checks', '❌ CONFIG对象未加载', 'error');
            }
            
            // JavaScript文件检查
            const jsObjects = [
                'CONFIG',
                'CloudinaryVideoManager', 
                'HybridCDNManager',
                'CloudinaryLoadBalancer',
                'SimpleVideoManager',
                'VideoManager'
            ];
            
            jsObjects.forEach(obj => {
                if (typeof window[obj] !== 'undefined') {
                    addResult('js-checks', `✅ ${obj} 已加载`, 'success');
                } else {
                    addResult('js-checks', `❌ ${obj} 未加载`, 'error');
                }
            });
            
            // 视频管理器检查
            setTimeout(() => {
                if (window.VideoManager) {
                    addResult('video-checks', '✅ VideoManager 存在', 'success');
                    addResult('video-checks', `初始化状态: ${window.VideoManager.isInitialized}`, 'info');
                    
                    if (window.VideoManager.cdnManager) {
                        addResult('video-checks', '✅ CDN管理器已初始化', 'success');
                    } else {
                        addResult('video-checks', '❌ CDN管理器未初始化', 'error');
                    }
                } else {
                    addResult('video-checks', '❌ VideoManager 不存在', 'error');
                }
            }, 2000);
            
            // 网络连接检查
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {
                    addResult('network-checks', '✅ API连接正常', 'success');
                    addResult('network-checks', `服务器响应: ${data.message}`, 'info');
                })
                .catch(error => {
                    addResult('network-checks', `❌ API连接失败: ${error.message}`, 'error');
                });
            
            // 视频文件检查
            fetch('/background/home/<USER>', {method: 'HEAD'})
                .then(response => {
                    if (response.ok) {
                        addResult('network-checks', '✅ 视频文件可访问', 'success');
                        addResult('network-checks', `视频文件大小: ${(response.headers.get('content-length') / 1024 / 1024).toFixed(2)} MB`, 'info');
                    } else {
                        addResult('network-checks', `❌ 视频文件访问失败: ${response.status}`, 'error');
                    }
                })
                .catch(error => {
                    addResult('network-checks', `❌ 视频文件检查失败: ${error.message}`, 'error');
                });
        }

        // 定期更新控制台日志
        function updateConsoleLogs() {
            const container = document.getElementById('console-logs');
            container.innerHTML = '';
            logs.forEach(log => {
                const div = document.createElement('div');
                div.className = log.type === 'error' ? 'error' : log.type === 'warn' ? 'warning' : 'info';
                div.innerHTML = `[${log.time}] ${log.type.toUpperCase()}: ${log.message}`;
                container.appendChild(div);
            });
        }

        // 页面加载完成后运行诊断
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 开始运行诊断...');
            runDiagnostics();
            
            // 每2秒更新一次控制台日志
            setInterval(updateConsoleLogs, 2000);
        });

        // 捕获未处理的错误
        window.addEventListener('error', function(event) {
            addResult('console-logs', `❌ JavaScript错误: ${event.error?.message || event.message}`, 'error');
            console.error('Uncaught error:', event.error);
        });

        window.addEventListener('unhandledrejection', function(event) {
            addResult('console-logs', `❌ Promise拒绝: ${event.reason}`, 'error');
            console.error('Unhandled promise rejection:', event.reason);
        });
    </script>
</body>
</html>
