# Love Website 项目结构说明

## 📁 项目目录结构

```
love/
├── 📄 核心文件
│   ├── index.html                    # 主页面
│   ├── config.js                     # 主配置文件
│   ├── server.js                     # 服务器文件
│   ├── package.json                  # 项目依赖
│   └── service-worker.js             # PWA服务工作者
│
├── 🎨 样式和脚本
│   ├── style.css                     # 主样式文件
│   ├── pages.css                     # 页面样式
│   ├── script.js                     # 主脚本文件
│   ├── page-navigation.js            # 页面导航
│   ├── simple-video-manager.js       # 视频管理器
│   ├── hybrid-cdn-manager.js         # 混合CDN管理器
│   ├── cloudinary-load-balancer.js   # Cloudinary负载均衡器
│   ├── performance-monitor.js        # 性能监控
│   ├── preload-manager.js            # 预加载管理器
│   ├── romantic-quotes.js            # 浪漫语录
│   ├── modern-quotes-data.js         # 现代语录数据
│   └── dynamic-styles.js             # 动态样式
│
├── 📱 页面文件
│   └── html/
│       ├── index.html                # 首页
│       ├── anniversary.html          # 纪念日页面
│       ├── meetings.html             # 相遇回忆页面
│       ├── memorial.html             # 纪念相册页面
│       ├── together-days.html        # 在一起的日子页面
│       └── modern-quotes-data.js     # 语录数据
│
├── 🎬 媒体资源
│   ├── background/                   # 视频背景文件
│   │   ├── cloudinary-ready/         # 压缩后的视频文件
│   │   ├── home/                     # 首页视频
│   │   ├── anniversary/              # 纪念日视频
│   │   ├── meetings/                 # 相遇回忆视频
│   │   ├── memorial/                 # 纪念相册视频
│   │   ├── together-days/            # 在一起的日子视频
│   │   └── erhai/                    # 洱海视频
│   └── fonts/                        # 字体文件
│       ├── Courgette-Regular.ttf
│       ├── GreatVibes-Regular.ttf
│       └── 字体文件...
│
├── ⚙️ 配置管理
│   └── config/
│       ├── CONFIG-README.md          # 配置说明
│       ├── config-manager.sh         # 配置管理脚本
│       ├── manage-config.sh          # 配置管理
│       ├── server-config.js          # 服务器配置
│       ├── update-config.js          # 配置更新
│       └── config-usage-examples.md  # 使用示例
│
├── 🛠️ 脚本工具
│   └── scripts/
│       ├── multi-account-upload.js           # 多账户上传
│       ├── test-multi-account-loading.js     # 多账户测试
│       ├── setup-multi-account-env.sh        # 环境设置
│       ├── deploy-multi-account.sh           # 多账户部署
│       ├── verify-multi-account-deployment.sh # 部署验证
│       ├── compress-videos-for-cloudinary.sh # 视频压缩
│       ├── upload-to-cloudinary.js           # Cloudinary上传
│       ├── video-optimizer-2k.sh             # 2K视频优化
│       ├── deploy-love.sh                    # 项目部署
│       ├── import-modern-quotes.js           # 导入语录
│       └── 其他工具脚本...
│
├── 🧪 测试和监控
│   └── test/
│       ├── multi-account-monitoring.html     # 多账户监控面板
│       └── test-multi-account-frontend.html  # 前端测试页面
│
├── 📊 数据存储
│   └── data/
│       ├── love_messages.db          # 消息数据库
│       ├── 现代美好情话千句搜索_.txt  # 情话数据
│       └── backups/                  # 数据备份
│
├── 📝 文档
│   └── docs/
│       ├── guide-love.md                           # 使用指南
│       ├── 总架构方案.md                           # 架构方案
│       ├── 多账户Cloudinary管理模式.md              # 多账户管理
│       ├── Cloudinary-CDN-负载均衡完整部署指南.md   # 部署指南
│       ├── 视频加载优化完整方案.md                  # 优化方案
│       ├── video-optimization-summary.md           # 优化总结
│       ├── deployment-report.md                    # 部署报告
│       └── test-execution-report.md                # 测试报告
│
├── 📋 报告 (新整理)
│   └── reports/
│       ├── test-reports/             # 测试报告
│       │   └── multi-account-test-report-*.md
│       ├── verification-reports/     # 验证报告
│       │   └── multi-account-verification-*.md
│       └── system-reports/           # 系统报告
│           └── system-diagnosis-report.md
│
├── 🔧 工具 (新整理)
│   └── tools/
│       ├── deployment/               # 部署工具
│       │   └── final-verification.sh
│       ├── testing/                  # 测试工具
│       │   └── system-diagnosis.sh
│       └── management/               # 管理工具
│           ├── manage-love.sh
│           └── update-all-pages.sh
│
├── 📦 归档 (新整理)
│   └── archive/
│       ├── backups/                  # 各种备份
│       │   ├── general-backup/       # 通用备份
│       │   ├── html-backups/         # HTML备份
│       │   └── scripts-backup/       # 脚本备份
│       └── old-files/                # 旧文件
│           ├── cloudinary-setup.js
│           ├── demo-cache-performance.js
│           └── enhanced-video-manager.js
│
├── 📚 依赖和临时文件
│   ├── node_modules/                 # Node.js依赖
│   ├── temp/                         # 临时文件
│   ├── logs/                         # 日志文件
│   ├── package-lock.json             # 依赖锁定
│   └── yarn.lock                     # Yarn锁定
│
└── 📖 项目说明
    ├── README.md                     # 项目说明
    └── PROJECT-STRUCTURE.md          # 本文件
```

## 🎯 核心功能模块

### 1. 多账户Cloudinary管理
- **配置**: `config.js` 中的 `MULTI_ACCOUNT` 配置
- **管理器**: `hybrid-cdn-manager.js` - 智能账户选择
- **负载均衡**: `cloudinary-load-balancer.js` - 负载分配
- **上传工具**: `scripts/multi-account-upload.js`
- **测试工具**: `scripts/test-multi-account-loading.js`

### 2. 视频背景系统
- **管理器**: `simple-video-manager.js` - 统一视频管理
- **预加载**: `preload-manager.js` - 智能预加载
- **性能监控**: `performance-monitor.js` - 性能追踪
- **优化脚本**: `scripts/video-optimizer-2k.sh`

### 3. 页面导航系统
- **导航器**: `page-navigation.js` - 页面切换
- **预加载**: 智能页面预加载
- **缓存**: 页面内容缓存

### 4. 监控和测试
- **多账户监控**: `test/multi-account-monitoring.html`
- **前端测试**: `test/test-multi-account-frontend.html`
- **性能报告**: `performance-report.js`

## 🚀 快速开始

### 启动项目
```bash
# 安装依赖
npm install

# 启动服务器
node server.js

# 或使用nodemon开发模式
npm run dev
```

### 多账户管理
```bash
# 设置环境变量
source scripts/setup-multi-account-env.sh

# 测试账户连接
node scripts/multi-account-upload.js test

# 上传视频到各账户
node scripts/multi-account-upload.js upload

# 测试多账户加载
node scripts/test-multi-account-loading.js test-all
```

### 部署和验证
```bash
# 完整部署
./scripts/deploy-multi-account.sh

# 验证部署
./scripts/verify-multi-account-deployment.sh
```

## 📊 监控链接

- **多账户监控面板**: https://love.yuh.cool/test/multi-account-monitoring.html
- **前端加载测试**: https://love.yuh.cool/test/test-multi-account-frontend.html
- **主网站**: https://love.yuh.cool/

## 📝 维护说明

### 日常维护
1. 检查 `reports/` 目录中的测试报告
2. 使用 `tools/testing/` 中的工具进行系统诊断
3. 查看 `logs/` 目录中的日志文件

### 备份管理
- 所有备份文件已移至 `archive/backups/`
- 旧文件已移至 `archive/old-files/`
- 定期清理临时文件和日志

### 文档更新
- 主要文档在 `docs/` 目录
- 测试报告在 `reports/` 目录
- 项目结构变更时更新本文件

---

**最后更新**: 2025-01-30  
**版本**: v2.0 (多账户管理模式)  
**维护者**: AI Assistant
